plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    id("maven-publish")
}

android {
    namespace = "cn.seres.auto.s2s.sdk.api"
    compileSdk = 34
    
    defaultConfig {
        minSdk = 31
        
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }
    
    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
        aidl = true
        buildConfig = true
    }
}

dependencies {
    
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation("com.google.code.gson:gson:2.8.6")
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}

// 发布nativeLib的publish任务
val publishVersion: String? = project.findProperty("publishVersion") as? String

afterEvaluate {
    publishing {
        publications {
            create<MavenPublication>("maven") {
                artifact(tasks.getByName("bundleReleaseAar"))
                artifact(sourcesJar)
                
                groupId = "com.seres.s2s"
                artifactId = "sdk-api"
                version = publishVersion ?: "0.0.1-SNAPSHOT"
                
                pom {
                    name.set("s2s-sdk-api")
                    packaging = "aar"
                    description.set("seres s2s android sdk-api")
                }
            }
        }
        repositories {
            maven {
                isAllowInsecureProtocol = true
                credentials {
                    username = "jmdev"
                    password = "CZPJ0Iz3Gi8j"
                }
                url = uri(
                    if (publishVersion?.contains("SNAPSHOT") == true) {
                        "https://repo.seres.cn/nexus/repository/maven-JM3.0-SNAPSHOT/"
                    } else {
                        "https://repo.seres.cn/nexus/repository/maven-JM3.0/"
                    }
                )
            }
        }
    }
}

val sourcesJar = tasks.create<Jar>("sourcesJar") {
    when {
        project.hasProperty("kotlin") -> {
            from(android.sourceSets["main"].java.srcDirs)
        }
        
        project.hasProperty("android") -> {
            from(android.sourceSets["main"].java.getSourceFiles())
        }
        
        else -> {
            println(project)
            from(sourceSets["main"].allSource)
        }
    }
    archiveClassifier.set("sources")
}

artifacts {
    add("archives", sourcesJar)
}