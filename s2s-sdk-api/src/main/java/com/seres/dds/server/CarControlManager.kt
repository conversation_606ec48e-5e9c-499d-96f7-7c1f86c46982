package com.seres.dds.server

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Parcelable
import android.os.RemoteException
import android.util.Log
import seres.s2s.internal.IAsyncResultCallback
import seres.s2s.internal.IS2SReportListener
import seres.s2s.internal.IS2SService
import kotlin.concurrent.Volatile

/**
 * 车控SOA相关服务接口封装
 *
 * @constructor Create empty Car control manager
 */
class CarControlManager private constructor() {

    companion object {
        private const val TAG = "CarControlManager"
        private const val MAX_ATTEMPTS = 20
        private const val RETRY_DELAY = 3000L

        private const val REMOTE_SERVER_PKG = "com.seres.dds"
        private const val REMOTE_SERVER_CLASS = "com.seres.dds.server.IpcServer"

        @Volatile
        var instance: CarControlManager? = null
            get() {
                if (field == null) {
                    synchronized(CarControlManager::class.java) {
                        if (field == null) {
                            field = CarControlManager()
                        }
                    }
                }
                return field
            }
            private set
    }

    private var mWorkerHandler: Handler? = null
    private var mService: IS2SService? = null
    private var mAttempts = 0
    private var carService = ServiceParamsMap()
    private var statusEventCallback: StatusEventCallback? = null
    fun init(context: Context) {
        carService.init()
        tryBindS2sServer(context)
    }

    private fun tryBindS2sServer(context: Context) {
        Log.i(TAG, "bindS2sServer ")
        val intent = Intent()
        intent.setComponent(ComponentName(REMOTE_SERVER_PKG, REMOTE_SERVER_CLASS))
        val result = context.bindService(intent, object : ServiceConnection {
            override fun onServiceConnected(name: ComponentName, service: IBinder) {
                Log.i(TAG, "onServiceConnected ")
                mService = IS2SService.Stub.asInterface(service)
                mAttempts = 0
            }

            override fun onServiceDisconnected(name: ComponentName) {
                if (mAttempts < MAX_ATTEMPTS) {
                    mWorkerHandler?.postDelayed({
                        mAttempts++
                        Log.w(TAG, "onServiceDisconnected ,try reconnect")
                        tryBindS2sServer(context)
                    }, RETRY_DELAY)
                }
            }
        }, Context.BIND_AUTO_CREATE)
        if (!result && mAttempts < MAX_ATTEMPTS) {
            mWorkerHandler?.postDelayed({
                mAttempts++
                Log.w(TAG, "bind failed ,try reconnect")
                tryBindS2sServer(context)
            }, RETRY_DELAY)
        }
    }


    /**
     * Register s2s signal listener
     *
     * @param callerId 调用方唯一身份id
     * @param signalHashIdList 需要监听的信号list
     * @param listener
     */
    fun registerS2SSignalListener(
        callerId: Int,
        signalHashIdList: IntArray?,
        listener: StatusEventCallback
    ) {
        Log.i(TAG, "registerS2SSignalListener")
        mService?.registerS2SSignalListener(callerId, s2sReportListener, signalHashIdList)
        statusEventCallback = listener
    }

    /**
     * Unregister s2s signal listener
     *
     * @param callerId 调用方唯一身份id
     */
    fun unregisterS2SSignalListener(callerId: Int) {
        Log.i(TAG, "unregisterS2SSignalListener")
        mService?.unregisterS2SSignalListener(callerId)
        statusEventCallback = null
    }

    /**
     * Sub s2s signal listen
     *
     * @param callerId 调用方唯一身份id
     * @param signalHashIdList 需要监听的信号list
     */
    fun subS2SSignalListen(callerId: Int, signalHashIdList: IntArray?) {
        Log.i(TAG, "subS2SSignalListen")
        mService?.subS2SSignalListen(callerId, signalHashIdList)
    }

    /**
     * Unsub s2s signal listen
     *
     * @param callerId 调用方唯一身份id
     * @param signalHashIdList 需要监听的信号list
     */
    fun unsubS2SSignalListen(callerId: Int, signalHashIdList: IntArray?) {
        Log.i(TAG, "unsubS2SSignalListen")
        mService?.unsubS2SSignalListen(callerId, signalHashIdList)
    }

    /**
     * 服务指令下发,结果同步返回
     *
     * @param callerId 调用方唯一身份id
     * @param serviceId 服务id
     * @param jsonStr 服务参数：jsonString
     * @return
     */
    fun invoke(callerId: Int, serviceId: Int, jsonStr: String): Bundle {
        Log.i(TAG, "invoke ,callerId : $callerId ,serviceId : $serviceId , jsonStr : $jsonStr")
        return invoke(
            callerId,
            serviceId,
            carService.jsonParam2Object(serviceId, jsonStr)!! as Bundle
        )
    }

    /**
     * 服务指令下发,结果同步返回
     *
     * @param callerId 调用方唯一身份id
     * @param serviceId 服务id
     * @param data 服务参数，序列化对象
     * @return
     */
    fun invoke(callerId: Int, serviceId: Int, data: Bundle): Bundle {
        Log.i(TAG, "invoke ,callerId : $callerId,serviceId : $serviceId , param : $data")
        try {
            return mService!!.invoke(callerId, serviceId, data)
        } catch (e: RemoteException) {
            Log.i(TAG, "invoke ,error : ${e.printStackTrace()}")
        }
        return Bundle()
    }

    /**
     * 服务指令下发,结果异步返回
     *
     * @param callerId 调用方唯一身份id
     * @param serviceId 服务id
     * @param jsonStr 服务参数：jsonString
     * @param callback 结果回调
     */
    fun invokeAsync(callerId: Int, serviceId: Int, jsonStr: String, callback: CmdEventCallback) {
        Log.i(TAG, "invokeAsync ,callerId : $callerId ,serviceId : $serviceId , jsonStr : $jsonStr")
        invokeAsync(
            callerId,
            serviceId,
            carService.jsonParam2Object(serviceId, jsonStr)!! as Bundle,
            callback
        )
    }

    /**
     * 服务指令下发,结果异步返回
     *
     * @param callerId 调用方唯一身份id
     * @param serviceId 服务id
     * @param params 服务参数，序列化对象
     * @param callback 结果回调
     */
    fun invokeAsync(callerId: Int, serviceId: Int, params: Bundle, callback: CmdEventCallback) {
        Log.i(TAG, "invokeAsync ,callerId : $callerId ,serviceId : $serviceId , param : $params")
        try {
            mService!!.invokeAsync(callerId, serviceId, params,
                object : IAsyncResultCallback.Stub() {
                    override fun onResult(p0: Bundle?) {
                        callback.onResult(serviceId, p0!!)
                    }
                })
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    private val s2sReportListener = object : IS2SReportListener.Stub() {
        override fun notify(data: Bundle?) {
            Log.w(TAG, "Receive s2s report message")
            statusEventCallback?.onCallback(data!!)
        }
    }

    /**
     * 指令下发的异步回调
     *
     * @constructor Create empty Cmd event callback
     */
    interface CmdEventCallback {
        fun onResult(serviceId: Int, data: Bundle)
    }

    /**
     * 车控服务状态监听回调
     *
     * @constructor Create empty Status event callback
     */
    interface StatusEventCallback {
        fun onCallback(data: Bundle)
    }
}
