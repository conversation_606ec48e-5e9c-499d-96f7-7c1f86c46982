package com.seres.dds.server.api

import android.os.Parcel
import android.os.Parcelable


data class BCM_VentilationCmd(
    var onOffCmd: OnOffCmd,
    var ventilationLevel: VentilationLevel
) : Parcelable {
    constructor(parcel: Parcel) : this(
        OnOffCmd.fromValue(parcel.readInt()),
        VentilationLevel.fromValue(parcel.readInt())
    )

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(onOffCmd.value)
        parcel.writeInt(ventilationLevel.value)
    }

    companion object CREATOR : Parcelable.Creator<BCM_VentilationCmd> {
        override fun createFromParcel(parcel: Parcel): BCM_VentilationCmd {
            return BCM_VentilationCmd(parcel)
        }

        override fun newArray(size: Int): Array<BCM_VentilationCmd?> {
            return arrayOfNulls(size)
        }
    }
}