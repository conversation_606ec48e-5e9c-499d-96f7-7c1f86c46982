/**
* Generate by D:\code\new-gerrit\SERES_DDS_TOOLS\excel_scan\test\【JM3.0项目】整车服务接口定义_控制器对外通信(0626)-end.xlsx
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-07-02 15:13:37.260473
*/


package com.seres.dds.server.api

enum class VentilationLevel(val value: Int) {
    VentilationLevel_No_Request(0x0),
    VentilationLevel_OFF(0x1),
    VentilationLevel_1(0x2),
    VentilationLevel_2(0x3),
    VentilationLevel_3(0x4),
    VentilationLevel_Invalid(0xFF);
    companion object {
        fun fromValue(value: Int): VentilationLevel {
            return VentilationLevel.values().first { it.value == value }
        }
    }
}