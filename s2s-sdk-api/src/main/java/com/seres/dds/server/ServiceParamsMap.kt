package com.seres.dds.server

import android.os.Bundle
import android.os.Parcelable
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.seres.dds.server.api.HPCC_MusicRhythmOp
import kotlin.reflect.KClass

class ServiceParamsMap {
    private val PARAMETER_CONVERSION_MAP = mutableMapOf<Int, FunctionWithType<*>>()
    private val mGson = Gson()

    fun init() {
        initParamsMap()
    }

    /**
     * 根据服务id将jsonString转为序列化对象参数
     *
     * @param id
     * @param json
     * @return
     */
    fun jsonParam2Object(id: Int,json:String) : Any? {
        return PARAMETER_CONVERSION_MAP[id]?.function?.invoke(json)
    }

    private fun initParamsMap() {
        PARAMETER_CONVERSION_MAP[-1648589748] = FunctionWithType(
            function = { json ->
                val jsonObject = mGson.fromJson(json,JsonObject::class.java)
                val bundle = Bundle()
                bundle.putInt(
                    "doorid",
                    mGson.fromJson(jsonObject.get("doorid"), Int::class.java)
                )
                bundle.putInt(
                    "maxtargetposition",
                    mGson.fromJson(jsonObject.get("maxtargetposition"), Int::class.java)
                )
                bundle
            },
            type = Bundle::class
        )
        PARAMETER_CONVERSION_MAP[15227906] = FunctionWithType(
            function = { json ->
                val jsonObject = mGson.fromJson(json,JsonObject::class.java)
                val bundle = Bundle()
                bundle.putParcelable(
                    "musicrhythmop",
                    mGson.fromJson(jsonObject.get("musicrhythmop"), HPCC_MusicRhythmOp::class.java) as Parcelable
                )
                bundle
            },
            type = Bundle::class
        )
    }

    class FunctionWithType<T : Any>(
        val function: (String) -> T,
        val type: KClass<T>
    )
}