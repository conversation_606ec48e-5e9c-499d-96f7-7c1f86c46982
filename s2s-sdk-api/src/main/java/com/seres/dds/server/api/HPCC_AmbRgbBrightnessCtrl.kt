/**
* Generate by D:\code\new-gerrit\SERES_DDS_TOOLS\excel_scan\test\【JM3.0项目】整车服务接口定义_控制器对外通信(0626)-end.xlsx
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-07-02 15:13:37.185849
*/

package com.seres.dds.server.api


import android.os.Parcel
import android.os.Parcelable


data class HPCC_AmbRgbBrightnessCtrl(
    var ambzoneid: Int,
    var brightness: Int,
    var rgb: Int
) : Parcelable {

    constructor(parcel: Parcel) : this(
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(ambzoneid)
        parcel.writeInt(brightness)
        parcel.writeInt(rgb)
        
    }

    override fun describeContents(): Int = 0

        companion object CREATOR : Parcelable.Creator<HPCC_AmbRgbBrightnessCtrl> {
        override fun createFromParcel(parcel: Parcel): HPCC_AmbRgbBrightnessCtrl {
            return HPCC_AmbRgbBrightnessCtrl(parcel)
        }

        override fun newArray(size: Int): Array<HPCC_AmbRgbBrightnessCtrl?> {
            return arrayOfNulls(size)
        }
    }
}