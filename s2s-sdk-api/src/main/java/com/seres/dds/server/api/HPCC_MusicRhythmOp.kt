/**
* Generate by D:\code\new-gerrit\SERES_DDS_TOOLS\excel_scan\test\【JM3.0项目】整车服务接口定义_控制器对外通信(0626)-end.xlsx
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-07-02 15:13:37.188462
*/

package com.seres.dds.server.api


import android.os.Parcel
import android.os.Parcelable


data class HPCC_MusicRhythmOp(
    var ambctrl: HPCC_MusicAmbCtrl,
    var ambset: HPCC_AmbRgbBrightnessCtrl,
    var schroederset: HPCC_SchroederLightCtrl
) : Parcelable {

    constructor(parcel: Parcel) : this(
        parcel.readParcelable(HPCC_MusicAmbCtrl::class.java.classLoader)!!,
        parcel.readParcelable(HPCC_AmbRgbBrightnessCtrl::class.java.classLoader)!!,
        parcel.readParcelable(HPCC_SchroederLightCtrl::class.java.classLoader)!!
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeParcelable(ambctrl, flags)
        parcel.writeParcelable(ambset, flags)
        parcel.writeParcelable(schroederset, flags)
        
    }

    override fun describeContents(): Int = 0

        companion object CREATOR : Parcelable.Creator<HPCC_MusicRhythmOp> {
        override fun createFromParcel(parcel: Parcel): HPCC_MusicRhythmOp {
            return HPCC_MusicRhythmOp(parcel)
        }

        override fun newArray(size: Int): Array<HPCC_MusicRhythmOp?> {
            return arrayOfNulls(size)
        }
    }
}