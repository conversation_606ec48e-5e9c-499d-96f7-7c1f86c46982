/**
* Generate by D:\code\new-gerrit\SERES_DDS_TOOLS\excel_scan\test\【JM3.0项目】整车服务接口定义_控制器对外通信(0626)-end.xlsx
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-07-02 15:13:37.184369
*/

package com.seres.dds.server.api


import android.os.Parcel
import android.os.Parcelable


data class HPCC_MusicAmbCtrl(
    var ambzoneid: Int,
    var onoffcmd: Int
) : Parcelable {

    constructor(parcel: Parcel) : this(
        parcel.readInt(),
        parcel.readInt()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(ambzoneid)
        parcel.writeInt(onoffcmd)
        
    }

    override fun describeContents(): Int = 0

        companion object CREATOR : Parcelable.Creator<HPCC_MusicAmbCtrl> {
        override fun createFromParcel(parcel: Parcel): HPCC_MusicAmbCtrl {
            return HPCC_MusicAmbCtrl(parcel)
        }

        override fun newArray(size: Int): Array<HPCC_MusicAmbCtrl?> {
            return arrayOfNulls(size)
        }
    }
}