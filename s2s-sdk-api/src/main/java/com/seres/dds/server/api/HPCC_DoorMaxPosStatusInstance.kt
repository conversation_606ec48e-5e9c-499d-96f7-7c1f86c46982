/**
* Generate by D:\code\new-gerrit\SERES_DDS_TOOLS\excel_scan\test\【JM3.0项目】整车服务接口定义_控制器对外通信(0626)-end.xlsx
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-07-02 15:13:37.159280
*/

package com.seres.dds.server.api


import android.os.Parcel
import android.os.Parcelable


data class HPCC_DoorMaxPosStatusInstance(
    var doorid: Int,
    var position: Int
) : Parcelable {

    constructor(parcel: Parcel) : this(
        parcel.readInt(),
        parcel.readInt()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(doorid)
        parcel.writeInt(position)
        
    }

    override fun describeContents(): Int = 0

        companion object CREATOR : Parcelable.Creator<HPCC_DoorMaxPosStatusInstance> {
        override fun createFromParcel(parcel: Parcel): HPCC_DoorMaxPosStatusInstance {
            return HPCC_DoorMaxPosStatusInstance(parcel)
        }

        override fun newArray(size: Int): Array<HPCC_DoorMaxPosStatusInstance?> {
            return arrayOfNulls(size)
        }
    }
}