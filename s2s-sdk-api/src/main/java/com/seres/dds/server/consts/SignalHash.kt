package com.seres.dds.server.consts

enum class SignalHash(val hashValue: Int) {
    heatingStatusFL_Hash(0),
    heatingLevelFL_Hash(1),
    ventilatingStatusFL_Hash(2),
    heatingStatusFR_Hash(3),
    ventilatingStatusFR_Hash(4),
    heatingLevelFR_Hash(5),
    mainXDirFL_Hash(6),
    mainXDirFR_Hash(7),
    ventilationFL_Hash(8),
    ventilationFR_Hash(9),
    seat_FL_XActuateStatus_Hash(10),
    seat_FR_XActuateStatus_Hash(11),
    commonlight_hood_Hash(12),
    positionFL_Hash(13),
    positionFR_Hash(14),
    commonlight_trunk_lightstatus_Hash(15)
}