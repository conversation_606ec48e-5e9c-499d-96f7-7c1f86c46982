<dependencies>
  <compile
      roots="org.jetbrains.kotlin:kotlin-reflect:1.8.0@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,org.jetbrains:annotations:13.0@jar">
    <dependency
        name="org.jetbrains.kotlin:kotlin-reflect:1.8.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-reflect"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
  </compile>
  <package
      roots="org.jetbrains.kotlin:kotlin-reflect:1.8.0@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar,org.jetbrains:annotations:13.0@jar">
    <dependency
        name="org.jetbrains.kotlin:kotlin-reflect:1.8.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-reflect"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
  </package>
</dependencies>
