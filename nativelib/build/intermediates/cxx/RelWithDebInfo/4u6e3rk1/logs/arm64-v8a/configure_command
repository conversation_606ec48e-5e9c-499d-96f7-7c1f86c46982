/usr/bin/cmake \
  -H/home/<USER>/dds/s2sService/nativelib/src/main/cpp \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=31 \
  -DANDROID_PLATFORM=android-31 \
  -DANDROID_ABI=arm64-v8a \
  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \
  -DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/27.2.12479018 \
  -DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/27.2.12479018 \
  -DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/27.2.12479018/build/cmake/android.toolchain.cmake \
  -DCMAKE_MAKE_PROGRAM=/home/<USER>/Android/Sdk/cmake/4.0.2/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/home/<USER>/dds/s2sService/nativelib/build/intermediates/cxx/RelWithDebInfo/4u6e3rk1/obj/arm64-v8a \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/home/<USER>/dds/s2sService/nativelib/build/intermediates/cxx/RelWithDebInfo/4u6e3rk1/obj/arm64-v8a \
  -DCMAKE_BUILD_TYPE=RelWithDebInfo \
  -B/home/<USER>/dds/s2sService/nativelib/.cxx/RelWithDebInfo/4u6e3rk1/arm64-v8a \
  -GNinja
