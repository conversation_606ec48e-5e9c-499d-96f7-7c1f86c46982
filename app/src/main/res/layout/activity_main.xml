<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="pubsub测试:"
            android:textSize="24dp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnStartSubscriber"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启动Subscriber" />

        <Button
            android:id="@+id/btnStartPublisher"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启动Publisher" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="rpc测试:"
            android:textSize="24dp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btnStartClient"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启动Client" />

        <Button
            android:id="@+id/btnStartServer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启动Server" />
    </LinearLayout>

    <Button
        android:id="@+id/btnSendMsgToAndroid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="向Android发送消息" />

    <Button
        android:id="@+id/btnStartPrintStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="开始显示BCM状态数据" />
    <Button
        android:id="@+id/btnStopPrintStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="停止显示BCM状态数据" />

    <TextView
        android:id="@+id/tvBtmStatusTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:textStyle="bold"
        android:text="车辆状态:" />

    <TextView
        android:id="@+id/tvBtmStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:textStyle="bold"
        tools:text="主驾驶座椅通风状态:打开" />

    <Button
        android:id="@+id/bind_service"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="注册监听" />
    <TextView
        android:id="@+id/on_msg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:textStyle="bold"
        tools:text="收到的数据:" />
    <Button
        android:id="@+id/send_data"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="发送消息" />
    <TextView
        android:id="@+id/sdk_test"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:textStyle="bold"
        tools:text="通信sdk" />
    <Button
        android:id="@+id/init_sdk"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="初始化sdk" />
    <Button
        android:id="@+id/send_data_to_soa"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="使用sdk发消息给soa,同步返回" />
    <Button
        android:id="@+id/send_data_to_soa_async"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="使用sdk发消息给soa，异步返回" />
</LinearLayout>
