package Seres.BCM_eSrv

enum class ACZoneId(var basevalue : Int){
    ACZONE_FRONT_LEFT(0),
    ACZONE_FRONT_RIGH(1),
    ACZONE_REAR_LEFT(2),
    ACZONE_REAR_RIGHT(3),
    ACZONE_THIRD_LEFT(4),
    ACZONE_THIRD_RIGHT(5),
    ACZoneId_INVALID(255);


    companion object {
        private val valueMap = ACZoneId.entries.associateBy { it.basevalue }
        fun fromValue(basevalue: Int): ACZoneId{
            return  valueMap[basevalue]?:ACZONE_FRONT_LEFT
        }    
    }
}
