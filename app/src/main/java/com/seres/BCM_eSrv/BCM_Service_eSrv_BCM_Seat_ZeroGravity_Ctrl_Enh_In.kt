package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_Seat_ZeroGravity_Ctrl_Enh_In : TypeStruct() {
    private var _seatid : Seres.BCM_eSrv.HPCC_SeatID = 0u
    private var _zerogravityparam : Seres.BCM_eSrv.HPCC_ZeroGravityPara = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_Seat_ZeroGravity_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_seatid", false),
            Member("_zerogravityparam", false),
        )

        initproperty()
    }

    var seatid: Seres.BCM_eSrv.HPCC_SeatID
        get() = _seatid
        set(value){
            _seatid = value
        }

    var zerogravityparam: Seres.BCM_eSrv.HPCC_ZeroGravityPara
        get() = _zerogravityparam
        set(value){
            _zerogravityparam = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_Seat_ZeroGravity_Ctrl_Enh_In = this): BCM_Service_eSrv_BCM_Seat_ZeroGravity_Ctrl_Enh_In{
            this._seatid =  value._seatid
            this._zerogravityparam =  value._zerogravityparam
            return this
        }

    override fun toString(): String{
        return "$typename(seatid=$seatid, zerogravityparam=$zerogravityparam)"
    }
}

