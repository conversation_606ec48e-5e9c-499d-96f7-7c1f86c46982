package Seres.BCM_eSrv

const val BCM_Service_eSrv_BCM_TailGate_Ctrl_Enh_HASH = 95479469
const val BCM_Service_eSrv_BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh_HASH = 35198907
const val BCM_Service_eSrv_BCM_FrntHatch_Ctrl_Enh_HASH = 116988609
const val BCM_Service_eSrv_BCM_ChrgPort_Ctrl_Enh_HASH = 262039799
const val BCM_Service_eSrv_BCM_FuelFiller_Ctrl_Enh_HASH = 24311010
const val BCM_Service_eSrv_BCM_SideDoor_ManuaMode_Ctrl_Enh_HASH = 24647378
const val BCM_Service_eSrv_BCM_SideDoor_MaxTargetPos_Ctrl_Enh_HASH = 157153507
const val BCM_Service_eSrv_BCM_SideDoor_OpenSpeed_Ctrl_Enh_HASH = 220966725
const val BCM_Service_eSrv_BCM_BrakePadCloseDoorMode_Ctrl_Enh_HASH = 150775107
const val BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_HASH = 7457417
const val BCM_Service_eSrv_TMS_Climate_Auto_Ctrl_Enh_HASH = 113471803
const val BCM_Service_eSrv_TMS_Climate_Temperature_Ctrl_Enh_HASH = 260335528
const val BCM_Service_eSrv_TMS_Climate_Temperature_Sync_Ctrl_Enh_HASH = 73129101
const val BCM_Service_eSrv_TMS_Climate_Level_Ctrl_Enh_HASH = 198175725
const val BCM_Service_eSrv_TMS_Climate_Mode_Ctrl_Enh_HASH = 7549938
const val BCM_Service_eSrv_TMS_MaxAsHeat_Status_Ctrl_Enh_HASH = 236642961
const val BCM_Service_eSrv_TMS_ACSwitch_Ctrl_Enh_HASH = 57273390
const val BCM_Service_eSrv_TMS_Demist_Ctrl_Enh_HASH = 208944869
const val BCM_Service_eSrv_TMS_Auto_Demist_Ctrl_Enh_HASH = 149237749
const val BCM_Service_eSrv_TMS_AirPurify_Ctrl_Enh_HASH = 1862893
const val BCM_Service_eSrv_TMS_AC_SetBox_Ctrl_Enh_HASH = 80205615
const val BCM_Service_eSrv_BCM_Auto_Ventilation_Ctrl_Enh_HASH = 43208485
const val BCM_Service_eSrv_EMS_RecircleMode_Ctrl_Enh_HASH = 33295031
const val BCM_Service_eSrv_EMS_SmartZones_Ctrl_Enh_HASH = 169684591
const val BCM_Service_eSrv_EMS_LowVoltage_Ctrl_Enh_HASH = 176051844
const val BCM_Service_eSrv_EMS_LowVoltage_Energy_Ctrl_Enh_HASH = 241890483
const val BCM_Service_eSrv_EMS_12V_PowerPort_Ctrl_Enh_HASH = 249557310
const val BCM_Service_eSrv_BCM_SeatCtrl_Enh_HASH = 171003682
const val BCM_Service_eSrv_BCM_Seat_ZeroGravity_Ctrl_Enh_HASH = 255237786
const val BCM_Service_eSrv_BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh_HASH = 73410753
const val BCM_Service_eSrv_BCM_Seat_Fold_Ctrl_Enh_HASH = 49402652
const val BCM_Service_eSrv_BCM_Seat_HeatLevel_Enh_HASH = 170021439
const val BCM_Service_eSrv_BCM_Seat_VentilationLevel_Enh_HASH = 99281418
const val BCM_Service_eSrv_BCM_Seat_MassageModeCtrl_Enh_HASH = 43574639
const val BCM_Service_eSrv_BCM_Seat_MassageStrengthCtrl_Enh_HASH = 208386874
const val BCM_Service_eSrv_BCM_Seat_ChildHeatVentilation_Enh_HASH = 26589401
const val BCM_Service_eSrv_BCM_Seat_ChildLeftBehind_Enh_HASH = 214610333
const val BCM_Service_eSrv_BCM_SunRoof_Ctrl_Enh_HASH = 3315914
const val BCM_Service_eSrv_BCM_SunRoof_AUTOLock_Enh_HASH = 43250664
const val BCM_Service_eSrv_BCM_RoofLight_Ctrl_Enh_HASH = 98483052
const val BCM_Service_eSrv_BCM_AmbientLight_Theme_Enh_HASH = 215961916
const val BCM_Service_eSrv_BCM_MusicRhyLight_Ctrl_Enh_HASH = 3900402
const val BCM_Service_eSrv_BCM_AmbientLight_Brightness_Ctrl_Enh_HASH = 179767365
const val BCM_Service_eSrv_BCM_AmbientLight_RGB_Ctrl_Enh_HASH = 94303614
const val BCM_Service_eSrv_BCM_AmbientLight_Sync_Enh_HASH = 261679203
const val BCM_Service_eSrv_BCM_WelLight_Ctrl_Enh_HASH = 266024731
const val BCM_Service_eSrv_BCM_NapMode_Ctrl_Enh_HASH = 132422306
const val BCM_Service_eSrv_BCM_DOW_BSD_Ctrl_Enh_HASH = 11398502
const val BCM_Service_eSrv_BCM_VoiceInteractionLight_Ctrl_Enh_HASH = 258667097
