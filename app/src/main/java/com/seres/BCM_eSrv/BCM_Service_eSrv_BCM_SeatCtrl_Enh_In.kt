package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_SeatCtrl_Enh_In : TypeStruct() {
    private var _seatposition : Seres.BCM_eSrv.HPCC_SeatPosition = Seres.BCM_eSrv.HPCC_SeatPosition()

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_SeatCtrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_seatposition", false),
        )

        initproperty()
    }

    var seatposition: Seres.BCM_eSrv.HPCC_SeatPosition
        get() = _seatposition
        set(value){
            _seatposition = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_SeatCtrl_Enh_In = this): BCM_Service_eSrv_BCM_SeatCtrl_Enh_In{
            this._seatposition =  value._seatposition
            return this
        }

    override fun toString(): String{
        return "$typename(seatposition=$seatposition)"
    }
}

