package Seres.BCM_eSrv

enum class MassagMode(var basevalue : Int){
    MassagMode_No_Request(0),
    MassagMode_OFF(1),
    MassagMode_1(2),
    MassagMode_2(3),
    MassagMode_3(4),
    MassagMode_INVALID(255);


    companion object {
        private val valueMap = MassagMode.entries.associateBy { it.basevalue }
        fun fromValue(basevalue: Int): MassagMode{
            return  valueMap[basevalue]?:MassagMode_No_Request
        }    
    }
}
