package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_TMS_MaxAsHeat_Status_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.BCM_eSrv.BCM_CallerID = 0u
    private var _maxacstatus : Seres.BCM_eSrv.HPCC_MaxACStatus = 0u
    private var _maxheatstatus : Seres.BCM_eSrv.HPCC_MaxHeatStatus = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_TMS_MaxAsHeat_Status_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_maxacstatus", false),
            Member("_maxheatstatus", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var maxacstatus: Seres.BCM_eSrv.HPCC_MaxACStatus
        get() = _maxacstatus
        set(value){
            _maxacstatus = value
        }

    var maxheatstatus: Seres.BCM_eSrv.HPCC_MaxHeatStatus
        get() = _maxheatstatus
        set(value){
            _maxheatstatus = value
        }

    fun copy(value: BCM_Service_eSrv_TMS_MaxAsHeat_Status_Ctrl_Enh_In = this): BCM_Service_eSrv_TMS_MaxAsHeat_Status_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._maxacstatus =  value._maxacstatus
            this._maxheatstatus =  value._maxheatstatus
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, maxacstatus=$maxacstatus, maxheatstatus=$maxheatstatus)"
    }
}

