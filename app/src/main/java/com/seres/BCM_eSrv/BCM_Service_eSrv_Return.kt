package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


open class BCM_Service_eSrv_Return : TypeUnion(){

    protected var __d: Int = 0
    protected var __u: Any? = null

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_Return"
        dmutableMap.put(95479469, descriptor_95479469::class)
        dmutableMap.put(35198907, descriptor_35198907::class)
        dmutableMap.put(116988609, descriptor_116988609::class)
        dmutableMap.put(262039799, descriptor_262039799::class)
        dmutableMap.put(24311010, descriptor_24311010::class)
        dmutableMap.put(24647378, descriptor_24647378::class)
        dmutableMap.put(157153507, descriptor_157153507::class)
        dmutableMap.put(220966725, descriptor_220966725::class)
        dmutableMap.put(150775107, descriptor_150775107::class)
        dmutableMap.put(7457417, descriptor_7457417::class)
        dmutableMap.put(113471803, descriptor_113471803::class)
        dmutableMap.put(260335528, descriptor_260335528::class)
        dmutableMap.put(73129101, descriptor_73129101::class)
        dmutableMap.put(198175725, descriptor_198175725::class)
        dmutableMap.put(7549938, descriptor_7549938::class)
        dmutableMap.put(236642961, descriptor_236642961::class)
        dmutableMap.put(57273390, descriptor_57273390::class)
        dmutableMap.put(208944869, descriptor_208944869::class)
        dmutableMap.put(149237749, descriptor_149237749::class)
        dmutableMap.put(1862893, descriptor_1862893::class)
        dmutableMap.put(80205615, descriptor_80205615::class)
        dmutableMap.put(43208485, descriptor_43208485::class)
        dmutableMap.put(33295031, descriptor_33295031::class)
        dmutableMap.put(169684591, descriptor_169684591::class)
        dmutableMap.put(176051844, descriptor_176051844::class)
        dmutableMap.put(241890483, descriptor_241890483::class)
        dmutableMap.put(249557310, descriptor_249557310::class)
        dmutableMap.put(171003682, descriptor_171003682::class)
        dmutableMap.put(255237786, descriptor_255237786::class)
        dmutableMap.put(73410753, descriptor_73410753::class)
        dmutableMap.put(49402652, descriptor_49402652::class)
        dmutableMap.put(170021439, descriptor_170021439::class)
        dmutableMap.put(99281418, descriptor_99281418::class)
        dmutableMap.put(43574639, descriptor_43574639::class)
        dmutableMap.put(208386874, descriptor_208386874::class)
        dmutableMap.put(26589401, descriptor_26589401::class)
        dmutableMap.put(214610333, descriptor_214610333::class)
        dmutableMap.put(3315914, descriptor_3315914::class)
        dmutableMap.put(43250664, descriptor_43250664::class)
        dmutableMap.put(98483052, descriptor_98483052::class)
        dmutableMap.put(215961916, descriptor_215961916::class)
        dmutableMap.put(3900402, descriptor_3900402::class)
        dmutableMap.put(179767365, descriptor_179767365::class)
        dmutableMap.put(94303614, descriptor_94303614::class)
        dmutableMap.put(261679203, descriptor_261679203::class)
        dmutableMap.put(266024731, descriptor_266024731::class)
        dmutableMap.put(132422306, descriptor_132422306::class)
        dmutableMap.put(11398502, descriptor_11398502::class)
        dmutableMap.put(258667097, descriptor_258667097::class)
    }

    var _d : Int
        get() = __d
        set(value) {
            __d = value
        }

    class descriptor_95479469() : BCM_Service_eSrv_Return(){
        private var _BCM_TailGate_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_TailGate_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_TailGate_Ctrl_Enh_Result()

        init{
            _d = 95479469
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_TailGate_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_TailGate_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_TailGate_Ctrl_Enh_Result
            get() = _BCM_TailGate_Ctrl_EnhResult
            set(value) {
                _BCM_TailGate_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_TailGate_Ctrl_EnhResult=$BCM_TailGate_Ctrl_EnhResult)"
        }

    }

    class descriptor_35198907() : BCM_Service_eSrv_Return(){
        private var _BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh_Result()

        init{
            _d = 35198907
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh_Result
            get() = _BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhResult
            set(value) {
                _BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhResult=$BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhResult)"
        }

    }

    class descriptor_116988609() : BCM_Service_eSrv_Return(){
        private var _BCM_FrntHatch_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_FrntHatch_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_FrntHatch_Ctrl_Enh_Result()

        init{
            _d = 116988609
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_FrntHatch_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_FrntHatch_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_FrntHatch_Ctrl_Enh_Result
            get() = _BCM_FrntHatch_Ctrl_EnhResult
            set(value) {
                _BCM_FrntHatch_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_FrntHatch_Ctrl_EnhResult=$BCM_FrntHatch_Ctrl_EnhResult)"
        }

    }

    class descriptor_262039799() : BCM_Service_eSrv_Return(){
        private var _BCM_ChrgPort_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_ChrgPort_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_ChrgPort_Ctrl_Enh_Result()

        init{
            _d = 262039799
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_ChrgPort_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_ChrgPort_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_ChrgPort_Ctrl_Enh_Result
            get() = _BCM_ChrgPort_Ctrl_EnhResult
            set(value) {
                _BCM_ChrgPort_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_ChrgPort_Ctrl_EnhResult=$BCM_ChrgPort_Ctrl_EnhResult)"
        }

    }

    class descriptor_24311010() : BCM_Service_eSrv_Return(){
        private var _BCM_FuelFiller_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_FuelFiller_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_FuelFiller_Ctrl_Enh_Result()

        init{
            _d = 24311010
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_FuelFiller_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_FuelFiller_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_FuelFiller_Ctrl_Enh_Result
            get() = _BCM_FuelFiller_Ctrl_EnhResult
            set(value) {
                _BCM_FuelFiller_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_FuelFiller_Ctrl_EnhResult=$BCM_FuelFiller_Ctrl_EnhResult)"
        }

    }

    class descriptor_24647378() : BCM_Service_eSrv_Return(){
        private var _BCM_SideDoor_ManuaMode_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SideDoor_ManuaMode_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SideDoor_ManuaMode_Ctrl_Enh_Result()

        init{
            _d = 24647378
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_SideDoor_ManuaMode_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_SideDoor_ManuaMode_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SideDoor_ManuaMode_Ctrl_Enh_Result
            get() = _BCM_SideDoor_ManuaMode_Ctrl_EnhResult
            set(value) {
                _BCM_SideDoor_ManuaMode_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_SideDoor_ManuaMode_Ctrl_EnhResult=$BCM_SideDoor_ManuaMode_Ctrl_EnhResult)"
        }

    }

    class descriptor_157153507() : BCM_Service_eSrv_Return(){
        private var _BCM_SideDoor_MaxTargetPos_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SideDoor_MaxTargetPos_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SideDoor_MaxTargetPos_Ctrl_Enh_Result()

        init{
            _d = 157153507
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_SideDoor_MaxTargetPos_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_SideDoor_MaxTargetPos_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SideDoor_MaxTargetPos_Ctrl_Enh_Result
            get() = _BCM_SideDoor_MaxTargetPos_Ctrl_EnhResult
            set(value) {
                _BCM_SideDoor_MaxTargetPos_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_SideDoor_MaxTargetPos_Ctrl_EnhResult=$BCM_SideDoor_MaxTargetPos_Ctrl_EnhResult)"
        }

    }

    class descriptor_220966725() : BCM_Service_eSrv_Return(){
        private var _BCM_SideDoor_OpenSpeed_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SideDoor_OpenSpeed_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SideDoor_OpenSpeed_Ctrl_Enh_Result()

        init{
            _d = 220966725
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_SideDoor_OpenSpeed_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_SideDoor_OpenSpeed_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SideDoor_OpenSpeed_Ctrl_Enh_Result
            get() = _BCM_SideDoor_OpenSpeed_Ctrl_EnhResult
            set(value) {
                _BCM_SideDoor_OpenSpeed_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_SideDoor_OpenSpeed_Ctrl_EnhResult=$BCM_SideDoor_OpenSpeed_Ctrl_EnhResult)"
        }

    }

    class descriptor_150775107() : BCM_Service_eSrv_Return(){
        private var _BCM_BrakePadCloseDoorMode_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_BrakePadCloseDoorMode_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_BrakePadCloseDoorMode_Ctrl_Enh_Result()

        init{
            _d = 150775107
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_BrakePadCloseDoorMode_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_BrakePadCloseDoorMode_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_BrakePadCloseDoorMode_Ctrl_Enh_Result
            get() = _BCM_BrakePadCloseDoorMode_Ctrl_EnhResult
            set(value) {
                _BCM_BrakePadCloseDoorMode_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_BrakePadCloseDoorMode_Ctrl_EnhResult=$BCM_BrakePadCloseDoorMode_Ctrl_EnhResult)"
        }

    }

    class descriptor_7457417() : BCM_Service_eSrv_Return(){
        private var _TMS_Climate_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_Result()

        init{
            _d = 7457417
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_Climate_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_Climate_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_Result
            get() = _TMS_Climate_Ctrl_EnhResult
            set(value) {
                _TMS_Climate_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_Climate_Ctrl_EnhResult=$TMS_Climate_Ctrl_EnhResult)"
        }

    }

    class descriptor_113471803() : BCM_Service_eSrv_Return(){
        private var _TMS_Climate_Auto_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Auto_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Auto_Ctrl_Enh_Result()

        init{
            _d = 113471803
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_Climate_Auto_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_Climate_Auto_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Auto_Ctrl_Enh_Result
            get() = _TMS_Climate_Auto_Ctrl_EnhResult
            set(value) {
                _TMS_Climate_Auto_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_Climate_Auto_Ctrl_EnhResult=$TMS_Climate_Auto_Ctrl_EnhResult)"
        }

    }

    class descriptor_260335528() : BCM_Service_eSrv_Return(){
        private var _TMS_Climate_Temperature_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Temperature_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Temperature_Ctrl_Enh_Result()

        init{
            _d = 260335528
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_Climate_Temperature_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_Climate_Temperature_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Temperature_Ctrl_Enh_Result
            get() = _TMS_Climate_Temperature_Ctrl_EnhResult
            set(value) {
                _TMS_Climate_Temperature_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_Climate_Temperature_Ctrl_EnhResult=$TMS_Climate_Temperature_Ctrl_EnhResult)"
        }

    }

    class descriptor_73129101() : BCM_Service_eSrv_Return(){
        private var _TMS_Climate_Temperature_Sync_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Temperature_Sync_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Temperature_Sync_Ctrl_Enh_Result()

        init{
            _d = 73129101
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_Climate_Temperature_Sync_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_Climate_Temperature_Sync_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Temperature_Sync_Ctrl_Enh_Result
            get() = _TMS_Climate_Temperature_Sync_Ctrl_EnhResult
            set(value) {
                _TMS_Climate_Temperature_Sync_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_Climate_Temperature_Sync_Ctrl_EnhResult=$TMS_Climate_Temperature_Sync_Ctrl_EnhResult)"
        }

    }

    class descriptor_198175725() : BCM_Service_eSrv_Return(){
        private var _TMS_Climate_Level_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Level_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Level_Ctrl_Enh_Result()

        init{
            _d = 198175725
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_Climate_Level_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_Climate_Level_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Level_Ctrl_Enh_Result
            get() = _TMS_Climate_Level_Ctrl_EnhResult
            set(value) {
                _TMS_Climate_Level_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_Climate_Level_Ctrl_EnhResult=$TMS_Climate_Level_Ctrl_EnhResult)"
        }

    }

    class descriptor_7549938() : BCM_Service_eSrv_Return(){
        private var _TMS_Climate_Mode_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Mode_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Mode_Ctrl_Enh_Result()

        init{
            _d = 7549938
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_Climate_Mode_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_Climate_Mode_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Mode_Ctrl_Enh_Result
            get() = _TMS_Climate_Mode_Ctrl_EnhResult
            set(value) {
                _TMS_Climate_Mode_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_Climate_Mode_Ctrl_EnhResult=$TMS_Climate_Mode_Ctrl_EnhResult)"
        }

    }

    class descriptor_236642961() : BCM_Service_eSrv_Return(){
        private var _TMS_MaxAsHeat_Status_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_MaxAsHeat_Status_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_MaxAsHeat_Status_Ctrl_Enh_Result()

        init{
            _d = 236642961
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_MaxAsHeat_Status_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_MaxAsHeat_Status_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_MaxAsHeat_Status_Ctrl_Enh_Result
            get() = _TMS_MaxAsHeat_Status_Ctrl_EnhResult
            set(value) {
                _TMS_MaxAsHeat_Status_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_MaxAsHeat_Status_Ctrl_EnhResult=$TMS_MaxAsHeat_Status_Ctrl_EnhResult)"
        }

    }

    class descriptor_57273390() : BCM_Service_eSrv_Return(){
        private var _TMS_ACSwitch_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_ACSwitch_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_ACSwitch_Ctrl_Enh_Result()

        init{
            _d = 57273390
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_ACSwitch_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_ACSwitch_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_ACSwitch_Ctrl_Enh_Result
            get() = _TMS_ACSwitch_Ctrl_EnhResult
            set(value) {
                _TMS_ACSwitch_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_ACSwitch_Ctrl_EnhResult=$TMS_ACSwitch_Ctrl_EnhResult)"
        }

    }

    class descriptor_208944869() : BCM_Service_eSrv_Return(){
        private var _TMS_Demist_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Demist_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Demist_Ctrl_Enh_Result()

        init{
            _d = 208944869
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_Demist_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_Demist_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Demist_Ctrl_Enh_Result
            get() = _TMS_Demist_Ctrl_EnhResult
            set(value) {
                _TMS_Demist_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_Demist_Ctrl_EnhResult=$TMS_Demist_Ctrl_EnhResult)"
        }

    }

    class descriptor_149237749() : BCM_Service_eSrv_Return(){
        private var _TMS_Auto_Demist_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Auto_Demist_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Auto_Demist_Ctrl_Enh_Result()

        init{
            _d = 149237749
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_Auto_Demist_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_Auto_Demist_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Auto_Demist_Ctrl_Enh_Result
            get() = _TMS_Auto_Demist_Ctrl_EnhResult
            set(value) {
                _TMS_Auto_Demist_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_Auto_Demist_Ctrl_EnhResult=$TMS_Auto_Demist_Ctrl_EnhResult)"
        }

    }

    class descriptor_1862893() : BCM_Service_eSrv_Return(){
        private var _TMS_AirPurify_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_AirPurify_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_AirPurify_Ctrl_Enh_Result()

        init{
            _d = 1862893
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_AirPurify_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_AirPurify_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_AirPurify_Ctrl_Enh_Result
            get() = _TMS_AirPurify_Ctrl_EnhResult
            set(value) {
                _TMS_AirPurify_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_AirPurify_Ctrl_EnhResult=$TMS_AirPurify_Ctrl_EnhResult)"
        }

    }

    class descriptor_80205615() : BCM_Service_eSrv_Return(){
        private var _TMS_AC_SetBox_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_AC_SetBox_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_AC_SetBox_Ctrl_Enh_Result()

        init{
            _d = 80205615
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_TMS_AC_SetBox_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var TMS_AC_SetBox_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_AC_SetBox_Ctrl_Enh_Result
            get() = _TMS_AC_SetBox_Ctrl_EnhResult
            set(value) {
                _TMS_AC_SetBox_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, TMS_AC_SetBox_Ctrl_EnhResult=$TMS_AC_SetBox_Ctrl_EnhResult)"
        }

    }

    class descriptor_43208485() : BCM_Service_eSrv_Return(){
        private var _BCM_Auto_Ventilation_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Auto_Ventilation_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Auto_Ventilation_Ctrl_Enh_Result()

        init{
            _d = 43208485
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Auto_Ventilation_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_Auto_Ventilation_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Auto_Ventilation_Ctrl_Enh_Result
            get() = _BCM_Auto_Ventilation_Ctrl_EnhResult
            set(value) {
                _BCM_Auto_Ventilation_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Auto_Ventilation_Ctrl_EnhResult=$BCM_Auto_Ventilation_Ctrl_EnhResult)"
        }

    }

    class descriptor_33295031() : BCM_Service_eSrv_Return(){
        private var _EMS_RecircleMode_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_EMS_RecircleMode_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_EMS_RecircleMode_Ctrl_Enh_Result()

        init{
            _d = 33295031
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_EMS_RecircleMode_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var EMS_RecircleMode_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_EMS_RecircleMode_Ctrl_Enh_Result
            get() = _EMS_RecircleMode_Ctrl_EnhResult
            set(value) {
                _EMS_RecircleMode_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, EMS_RecircleMode_Ctrl_EnhResult=$EMS_RecircleMode_Ctrl_EnhResult)"
        }

    }

    class descriptor_169684591() : BCM_Service_eSrv_Return(){
        private var _EMS_SmartZones_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_EMS_SmartZones_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_EMS_SmartZones_Ctrl_Enh_Result()

        init{
            _d = 169684591
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_EMS_SmartZones_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var EMS_SmartZones_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_EMS_SmartZones_Ctrl_Enh_Result
            get() = _EMS_SmartZones_Ctrl_EnhResult
            set(value) {
                _EMS_SmartZones_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, EMS_SmartZones_Ctrl_EnhResult=$EMS_SmartZones_Ctrl_EnhResult)"
        }

    }

    class descriptor_176051844() : BCM_Service_eSrv_Return(){
        private var _EMS_LowVoltage_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_EMS_LowVoltage_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_EMS_LowVoltage_Ctrl_Enh_Result()

        init{
            _d = 176051844
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_EMS_LowVoltage_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var EMS_LowVoltage_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_EMS_LowVoltage_Ctrl_Enh_Result
            get() = _EMS_LowVoltage_Ctrl_EnhResult
            set(value) {
                _EMS_LowVoltage_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, EMS_LowVoltage_Ctrl_EnhResult=$EMS_LowVoltage_Ctrl_EnhResult)"
        }

    }

    class descriptor_241890483() : BCM_Service_eSrv_Return(){
        private var _EMS_LowVoltage_Energy_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_EMS_LowVoltage_Energy_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_EMS_LowVoltage_Energy_Ctrl_Enh_Result()

        init{
            _d = 241890483
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_EMS_LowVoltage_Energy_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var EMS_LowVoltage_Energy_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_EMS_LowVoltage_Energy_Ctrl_Enh_Result
            get() = _EMS_LowVoltage_Energy_Ctrl_EnhResult
            set(value) {
                _EMS_LowVoltage_Energy_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, EMS_LowVoltage_Energy_Ctrl_EnhResult=$EMS_LowVoltage_Energy_Ctrl_EnhResult)"
        }

    }

    class descriptor_249557310() : BCM_Service_eSrv_Return(){
        private var _EMS_12V_PowerPort_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_EMS_12V_PowerPort_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_EMS_12V_PowerPort_Ctrl_Enh_Result()

        init{
            _d = 249557310
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_EMS_12V_PowerPort_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var EMS_12V_PowerPort_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_EMS_12V_PowerPort_Ctrl_Enh_Result
            get() = _EMS_12V_PowerPort_Ctrl_EnhResult
            set(value) {
                _EMS_12V_PowerPort_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, EMS_12V_PowerPort_Ctrl_EnhResult=$EMS_12V_PowerPort_Ctrl_EnhResult)"
        }

    }

    class descriptor_171003682() : BCM_Service_eSrv_Return(){
        private var _BCM_SeatCtrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SeatCtrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SeatCtrl_Enh_Result()

        init{
            _d = 171003682
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_SeatCtrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_SeatCtrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SeatCtrl_Enh_Result
            get() = _BCM_SeatCtrl_EnhResult
            set(value) {
                _BCM_SeatCtrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_SeatCtrl_EnhResult=$BCM_SeatCtrl_EnhResult)"
        }

    }

    class descriptor_255237786() : BCM_Service_eSrv_Return(){
        private var _BCM_Seat_ZeroGravity_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ZeroGravity_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ZeroGravity_Ctrl_Enh_Result()

        init{
            _d = 255237786
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Seat_ZeroGravity_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_Seat_ZeroGravity_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ZeroGravity_Ctrl_Enh_Result
            get() = _BCM_Seat_ZeroGravity_Ctrl_EnhResult
            set(value) {
                _BCM_Seat_ZeroGravity_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Seat_ZeroGravity_Ctrl_EnhResult=$BCM_Seat_ZeroGravity_Ctrl_EnhResult)"
        }

    }

    class descriptor_73410753() : BCM_Service_eSrv_Return(){
        private var _BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh_Result()

        init{
            _d = 73410753
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh_Result
            get() = _BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhResult
            set(value) {
                _BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhResult=$BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhResult)"
        }

    }

    class descriptor_49402652() : BCM_Service_eSrv_Return(){
        private var _BCM_Seat_Fold_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_Fold_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_Fold_Ctrl_Enh_Result()

        init{
            _d = 49402652
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Seat_Fold_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_Seat_Fold_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_Fold_Ctrl_Enh_Result
            get() = _BCM_Seat_Fold_Ctrl_EnhResult
            set(value) {
                _BCM_Seat_Fold_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Seat_Fold_Ctrl_EnhResult=$BCM_Seat_Fold_Ctrl_EnhResult)"
        }

    }

    class descriptor_170021439() : BCM_Service_eSrv_Return(){
        private var _BCM_Seat_HeatLevel_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_HeatLevel_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_HeatLevel_Enh_Result()

        init{
            _d = 170021439
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Seat_HeatLevel_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_Seat_HeatLevel_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_HeatLevel_Enh_Result
            get() = _BCM_Seat_HeatLevel_EnhResult
            set(value) {
                _BCM_Seat_HeatLevel_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Seat_HeatLevel_EnhResult=$BCM_Seat_HeatLevel_EnhResult)"
        }

    }

    class descriptor_99281418() : BCM_Service_eSrv_Return(){
        private var _BCM_Seat_VentilationLevel_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_VentilationLevel_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_VentilationLevel_Enh_Result()

        init{
            _d = 99281418
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Seat_VentilationLevel_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_Seat_VentilationLevel_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_VentilationLevel_Enh_Result
            get() = _BCM_Seat_VentilationLevel_EnhResult
            set(value) {
                _BCM_Seat_VentilationLevel_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Seat_VentilationLevel_EnhResult=$BCM_Seat_VentilationLevel_EnhResult)"
        }

    }

    class descriptor_43574639() : BCM_Service_eSrv_Return(){
        private var _BCM_Seat_MassageModeCtrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_MassageModeCtrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_MassageModeCtrl_Enh_Result()

        init{
            _d = 43574639
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Seat_MassageModeCtrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_Seat_MassageModeCtrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_MassageModeCtrl_Enh_Result
            get() = _BCM_Seat_MassageModeCtrl_EnhResult
            set(value) {
                _BCM_Seat_MassageModeCtrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Seat_MassageModeCtrl_EnhResult=$BCM_Seat_MassageModeCtrl_EnhResult)"
        }

    }

    class descriptor_208386874() : BCM_Service_eSrv_Return(){
        private var _BCM_Seat_MassageStrengthCtrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_MassageStrengthCtrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_MassageStrengthCtrl_Enh_Result()

        init{
            _d = 208386874
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Seat_MassageStrengthCtrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_Seat_MassageStrengthCtrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_MassageStrengthCtrl_Enh_Result
            get() = _BCM_Seat_MassageStrengthCtrl_EnhResult
            set(value) {
                _BCM_Seat_MassageStrengthCtrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Seat_MassageStrengthCtrl_EnhResult=$BCM_Seat_MassageStrengthCtrl_EnhResult)"
        }

    }

    class descriptor_26589401() : BCM_Service_eSrv_Return(){
        private var _BCM_Seat_ChildHeatVentilation_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ChildHeatVentilation_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ChildHeatVentilation_Enh_Result()

        init{
            _d = 26589401
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Seat_ChildHeatVentilation_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_Seat_ChildHeatVentilation_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ChildHeatVentilation_Enh_Result
            get() = _BCM_Seat_ChildHeatVentilation_EnhResult
            set(value) {
                _BCM_Seat_ChildHeatVentilation_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Seat_ChildHeatVentilation_EnhResult=$BCM_Seat_ChildHeatVentilation_EnhResult)"
        }

    }

    class descriptor_214610333() : BCM_Service_eSrv_Return(){
        private var _BCM_Seat_ChildLeftBehind_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ChildLeftBehind_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ChildLeftBehind_Enh_Result()

        init{
            _d = 214610333
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Seat_ChildLeftBehind_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_Seat_ChildLeftBehind_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_Seat_ChildLeftBehind_Enh_Result
            get() = _BCM_Seat_ChildLeftBehind_EnhResult
            set(value) {
                _BCM_Seat_ChildLeftBehind_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Seat_ChildLeftBehind_EnhResult=$BCM_Seat_ChildLeftBehind_EnhResult)"
        }

    }

    class descriptor_3315914() : BCM_Service_eSrv_Return(){
        private var _BCM_SunRoof_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SunRoof_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SunRoof_Ctrl_Enh_Result()

        init{
            _d = 3315914
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_SunRoof_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_SunRoof_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SunRoof_Ctrl_Enh_Result
            get() = _BCM_SunRoof_Ctrl_EnhResult
            set(value) {
                _BCM_SunRoof_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_SunRoof_Ctrl_EnhResult=$BCM_SunRoof_Ctrl_EnhResult)"
        }

    }

    class descriptor_43250664() : BCM_Service_eSrv_Return(){
        private var _BCM_SunRoof_AUTOLock_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SunRoof_AUTOLock_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SunRoof_AUTOLock_Enh_Result()

        init{
            _d = 43250664
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_SunRoof_AUTOLock_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_SunRoof_AUTOLock_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_SunRoof_AUTOLock_Enh_Result
            get() = _BCM_SunRoof_AUTOLock_EnhResult
            set(value) {
                _BCM_SunRoof_AUTOLock_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_SunRoof_AUTOLock_EnhResult=$BCM_SunRoof_AUTOLock_EnhResult)"
        }

    }

    class descriptor_98483052() : BCM_Service_eSrv_Return(){
        private var _BCM_RoofLight_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_RoofLight_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_RoofLight_Ctrl_Enh_Result()

        init{
            _d = 98483052
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_RoofLight_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_RoofLight_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_RoofLight_Ctrl_Enh_Result
            get() = _BCM_RoofLight_Ctrl_EnhResult
            set(value) {
                _BCM_RoofLight_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_RoofLight_Ctrl_EnhResult=$BCM_RoofLight_Ctrl_EnhResult)"
        }

    }

    class descriptor_215961916() : BCM_Service_eSrv_Return(){
        private var _BCM_AmbientLight_Theme_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_Theme_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_Theme_Enh_Result()

        init{
            _d = 215961916
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_AmbientLight_Theme_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_AmbientLight_Theme_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_Theme_Enh_Result
            get() = _BCM_AmbientLight_Theme_EnhResult
            set(value) {
                _BCM_AmbientLight_Theme_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_AmbientLight_Theme_EnhResult=$BCM_AmbientLight_Theme_EnhResult)"
        }

    }

    class descriptor_3900402() : BCM_Service_eSrv_Return(){
        private var _BCM_MusicRhyLight_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_MusicRhyLight_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_MusicRhyLight_Ctrl_Enh_Result()

        init{
            _d = 3900402
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_MusicRhyLight_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_MusicRhyLight_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_MusicRhyLight_Ctrl_Enh_Result
            get() = _BCM_MusicRhyLight_Ctrl_EnhResult
            set(value) {
                _BCM_MusicRhyLight_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_MusicRhyLight_Ctrl_EnhResult=$BCM_MusicRhyLight_Ctrl_EnhResult)"
        }

    }

    class descriptor_179767365() : BCM_Service_eSrv_Return(){
        private var _BCM_AmbientLight_Brightness_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_Brightness_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_Brightness_Ctrl_Enh_Result()

        init{
            _d = 179767365
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_AmbientLight_Brightness_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_AmbientLight_Brightness_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_Brightness_Ctrl_Enh_Result
            get() = _BCM_AmbientLight_Brightness_Ctrl_EnhResult
            set(value) {
                _BCM_AmbientLight_Brightness_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_AmbientLight_Brightness_Ctrl_EnhResult=$BCM_AmbientLight_Brightness_Ctrl_EnhResult)"
        }

    }

    class descriptor_94303614() : BCM_Service_eSrv_Return(){
        private var _BCM_AmbientLight_RGB_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_RGB_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_RGB_Ctrl_Enh_Result()

        init{
            _d = 94303614
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_AmbientLight_RGB_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_AmbientLight_RGB_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_RGB_Ctrl_Enh_Result
            get() = _BCM_AmbientLight_RGB_Ctrl_EnhResult
            set(value) {
                _BCM_AmbientLight_RGB_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_AmbientLight_RGB_Ctrl_EnhResult=$BCM_AmbientLight_RGB_Ctrl_EnhResult)"
        }

    }

    class descriptor_261679203() : BCM_Service_eSrv_Return(){
        private var _BCM_AmbientLight_Sync_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_Sync_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_Sync_Enh_Result()

        init{
            _d = 261679203
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_AmbientLight_Sync_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_AmbientLight_Sync_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_AmbientLight_Sync_Enh_Result
            get() = _BCM_AmbientLight_Sync_EnhResult
            set(value) {
                _BCM_AmbientLight_Sync_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_AmbientLight_Sync_EnhResult=$BCM_AmbientLight_Sync_EnhResult)"
        }

    }

    class descriptor_266024731() : BCM_Service_eSrv_Return(){
        private var _BCM_WelLight_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_WelLight_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_WelLight_Ctrl_Enh_Result()

        init{
            _d = 266024731
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_WelLight_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_WelLight_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_WelLight_Ctrl_Enh_Result
            get() = _BCM_WelLight_Ctrl_EnhResult
            set(value) {
                _BCM_WelLight_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_WelLight_Ctrl_EnhResult=$BCM_WelLight_Ctrl_EnhResult)"
        }

    }

    class descriptor_132422306() : BCM_Service_eSrv_Return(){
        private var _BCM_NapMode_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_NapMode_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_NapMode_Ctrl_Enh_Result()

        init{
            _d = 132422306
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_NapMode_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_NapMode_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_NapMode_Ctrl_Enh_Result
            get() = _BCM_NapMode_Ctrl_EnhResult
            set(value) {
                _BCM_NapMode_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_NapMode_Ctrl_EnhResult=$BCM_NapMode_Ctrl_EnhResult)"
        }

    }

    class descriptor_11398502() : BCM_Service_eSrv_Return(){
        private var _BCM_DOW_BSD_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_DOW_BSD_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_DOW_BSD_Ctrl_Enh_Result()

        init{
            _d = 11398502
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_DOW_BSD_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_DOW_BSD_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_DOW_BSD_Ctrl_Enh_Result
            get() = _BCM_DOW_BSD_Ctrl_EnhResult
            set(value) {
                _BCM_DOW_BSD_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_DOW_BSD_Ctrl_EnhResult=$BCM_DOW_BSD_Ctrl_EnhResult)"
        }

    }

    class descriptor_258667097() : BCM_Service_eSrv_Return(){
        private var _BCM_VoiceInteractionLight_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_VoiceInteractionLight_Ctrl_Enh_Result = Seres.BCM_eSrv.BCM_Service_eSrv_BCM_VoiceInteractionLight_Ctrl_Enh_Result()

        init{
            _d = 258667097
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_VoiceInteractionLight_Ctrl_EnhResult", false))

            __u = this

            initproperty()
        }

        var BCM_VoiceInteractionLight_Ctrl_EnhResult: Seres.BCM_eSrv.BCM_Service_eSrv_BCM_VoiceInteractionLight_Ctrl_Enh_Result
            get() = _BCM_VoiceInteractionLight_Ctrl_EnhResult
            set(value) {
                _BCM_VoiceInteractionLight_Ctrl_EnhResult = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_VoiceInteractionLight_Ctrl_EnhResult=$BCM_VoiceInteractionLight_Ctrl_EnhResult)"
        }

    }

    var _u_descriptor_95479469: descriptor_95479469
    get(){
        __u?.let{
            if ((__u as descriptor_95479469)._d == 95479469){
                return __u!! as descriptor_95479469
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_35198907: descriptor_35198907
    get(){
        __u?.let{
            if ((__u as descriptor_35198907)._d == 35198907){
                return __u!! as descriptor_35198907
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_116988609: descriptor_116988609
    get(){
        __u?.let{
            if ((__u as descriptor_116988609)._d == 116988609){
                return __u!! as descriptor_116988609
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_262039799: descriptor_262039799
    get(){
        __u?.let{
            if ((__u as descriptor_262039799)._d == 262039799){
                return __u!! as descriptor_262039799
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_24311010: descriptor_24311010
    get(){
        __u?.let{
            if ((__u as descriptor_24311010)._d == 24311010){
                return __u!! as descriptor_24311010
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_24647378: descriptor_24647378
    get(){
        __u?.let{
            if ((__u as descriptor_24647378)._d == 24647378){
                return __u!! as descriptor_24647378
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_157153507: descriptor_157153507
    get(){
        __u?.let{
            if ((__u as descriptor_157153507)._d == 157153507){
                return __u!! as descriptor_157153507
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_220966725: descriptor_220966725
    get(){
        __u?.let{
            if ((__u as descriptor_220966725)._d == 220966725){
                return __u!! as descriptor_220966725
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_150775107: descriptor_150775107
    get(){
        __u?.let{
            if ((__u as descriptor_150775107)._d == 150775107){
                return __u!! as descriptor_150775107
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_7457417: descriptor_7457417
    get(){
        __u?.let{
            if ((__u as descriptor_7457417)._d == 7457417){
                return __u!! as descriptor_7457417
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_113471803: descriptor_113471803
    get(){
        __u?.let{
            if ((__u as descriptor_113471803)._d == 113471803){
                return __u!! as descriptor_113471803
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_260335528: descriptor_260335528
    get(){
        __u?.let{
            if ((__u as descriptor_260335528)._d == 260335528){
                return __u!! as descriptor_260335528
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_73129101: descriptor_73129101
    get(){
        __u?.let{
            if ((__u as descriptor_73129101)._d == 73129101){
                return __u!! as descriptor_73129101
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_198175725: descriptor_198175725
    get(){
        __u?.let{
            if ((__u as descriptor_198175725)._d == 198175725){
                return __u!! as descriptor_198175725
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_7549938: descriptor_7549938
    get(){
        __u?.let{
            if ((__u as descriptor_7549938)._d == 7549938){
                return __u!! as descriptor_7549938
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_236642961: descriptor_236642961
    get(){
        __u?.let{
            if ((__u as descriptor_236642961)._d == 236642961){
                return __u!! as descriptor_236642961
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_57273390: descriptor_57273390
    get(){
        __u?.let{
            if ((__u as descriptor_57273390)._d == 57273390){
                return __u!! as descriptor_57273390
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_208944869: descriptor_208944869
    get(){
        __u?.let{
            if ((__u as descriptor_208944869)._d == 208944869){
                return __u!! as descriptor_208944869
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_149237749: descriptor_149237749
    get(){
        __u?.let{
            if ((__u as descriptor_149237749)._d == 149237749){
                return __u!! as descriptor_149237749
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_1862893: descriptor_1862893
    get(){
        __u?.let{
            if ((__u as descriptor_1862893)._d == 1862893){
                return __u!! as descriptor_1862893
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_80205615: descriptor_80205615
    get(){
        __u?.let{
            if ((__u as descriptor_80205615)._d == 80205615){
                return __u!! as descriptor_80205615
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_43208485: descriptor_43208485
    get(){
        __u?.let{
            if ((__u as descriptor_43208485)._d == 43208485){
                return __u!! as descriptor_43208485
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_33295031: descriptor_33295031
    get(){
        __u?.let{
            if ((__u as descriptor_33295031)._d == 33295031){
                return __u!! as descriptor_33295031
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_169684591: descriptor_169684591
    get(){
        __u?.let{
            if ((__u as descriptor_169684591)._d == 169684591){
                return __u!! as descriptor_169684591
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_176051844: descriptor_176051844
    get(){
        __u?.let{
            if ((__u as descriptor_176051844)._d == 176051844){
                return __u!! as descriptor_176051844
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_241890483: descriptor_241890483
    get(){
        __u?.let{
            if ((__u as descriptor_241890483)._d == 241890483){
                return __u!! as descriptor_241890483
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_249557310: descriptor_249557310
    get(){
        __u?.let{
            if ((__u as descriptor_249557310)._d == 249557310){
                return __u!! as descriptor_249557310
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_171003682: descriptor_171003682
    get(){
        __u?.let{
            if ((__u as descriptor_171003682)._d == 171003682){
                return __u!! as descriptor_171003682
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_255237786: descriptor_255237786
    get(){
        __u?.let{
            if ((__u as descriptor_255237786)._d == 255237786){
                return __u!! as descriptor_255237786
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_73410753: descriptor_73410753
    get(){
        __u?.let{
            if ((__u as descriptor_73410753)._d == 73410753){
                return __u!! as descriptor_73410753
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_49402652: descriptor_49402652
    get(){
        __u?.let{
            if ((__u as descriptor_49402652)._d == 49402652){
                return __u!! as descriptor_49402652
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_170021439: descriptor_170021439
    get(){
        __u?.let{
            if ((__u as descriptor_170021439)._d == 170021439){
                return __u!! as descriptor_170021439
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_99281418: descriptor_99281418
    get(){
        __u?.let{
            if ((__u as descriptor_99281418)._d == 99281418){
                return __u!! as descriptor_99281418
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_43574639: descriptor_43574639
    get(){
        __u?.let{
            if ((__u as descriptor_43574639)._d == 43574639){
                return __u!! as descriptor_43574639
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_208386874: descriptor_208386874
    get(){
        __u?.let{
            if ((__u as descriptor_208386874)._d == 208386874){
                return __u!! as descriptor_208386874
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_26589401: descriptor_26589401
    get(){
        __u?.let{
            if ((__u as descriptor_26589401)._d == 26589401){
                return __u!! as descriptor_26589401
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_214610333: descriptor_214610333
    get(){
        __u?.let{
            if ((__u as descriptor_214610333)._d == 214610333){
                return __u!! as descriptor_214610333
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_3315914: descriptor_3315914
    get(){
        __u?.let{
            if ((__u as descriptor_3315914)._d == 3315914){
                return __u!! as descriptor_3315914
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_43250664: descriptor_43250664
    get(){
        __u?.let{
            if ((__u as descriptor_43250664)._d == 43250664){
                return __u!! as descriptor_43250664
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_98483052: descriptor_98483052
    get(){
        __u?.let{
            if ((__u as descriptor_98483052)._d == 98483052){
                return __u!! as descriptor_98483052
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_215961916: descriptor_215961916
    get(){
        __u?.let{
            if ((__u as descriptor_215961916)._d == 215961916){
                return __u!! as descriptor_215961916
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_3900402: descriptor_3900402
    get(){
        __u?.let{
            if ((__u as descriptor_3900402)._d == 3900402){
                return __u!! as descriptor_3900402
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_179767365: descriptor_179767365
    get(){
        __u?.let{
            if ((__u as descriptor_179767365)._d == 179767365){
                return __u!! as descriptor_179767365
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_94303614: descriptor_94303614
    get(){
        __u?.let{
            if ((__u as descriptor_94303614)._d == 94303614){
                return __u!! as descriptor_94303614
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_261679203: descriptor_261679203
    get(){
        __u?.let{
            if ((__u as descriptor_261679203)._d == 261679203){
                return __u!! as descriptor_261679203
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_266024731: descriptor_266024731
    get(){
        __u?.let{
            if ((__u as descriptor_266024731)._d == 266024731){
                return __u!! as descriptor_266024731
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_132422306: descriptor_132422306
    get(){
        __u?.let{
            if ((__u as descriptor_132422306)._d == 132422306){
                return __u!! as descriptor_132422306
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_11398502: descriptor_11398502
    get(){
        __u?.let{
            if ((__u as descriptor_11398502)._d == 11398502){
                return __u!! as descriptor_11398502
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_258667097: descriptor_258667097
    get(){
        __u?.let{
            if ((__u as descriptor_258667097)._d == 258667097){
                return __u!! as descriptor_258667097
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }


}

