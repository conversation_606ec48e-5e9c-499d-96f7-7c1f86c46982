package Seres.BCM_eSrv

enum class HeatingLevel(var basevalue : Int){
    HeatingLevel_No_Request(0),
    HeatingLevel_OFF(1),
    HeatingLevel_1(2),
    HeatingLevel_2(3),
    HeatingLevel_3(4),
    HeatingLevel_Invalid(255);


    companion object {
        private val valueMap = HeatingLevel.entries.associateBy { it.basevalue }
        fun fromValue(basevalue: Int): HeatingLevel{
            return  valueMap[basevalue]?:HeatingLevel_No_Request
        }    
    }
}
