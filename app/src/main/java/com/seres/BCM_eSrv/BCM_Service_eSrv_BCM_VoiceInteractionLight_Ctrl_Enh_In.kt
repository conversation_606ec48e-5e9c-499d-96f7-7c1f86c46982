package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_VoiceInteractionLight_Ctrl_Enh_In : TypeStruct() {
    private var _onoffcmd : Seres.BCM_eSrv.HPCC_OnOffCmd = 0u
    private var _voicelightmode : Seres.BCM_eSrv.HPCC_VoiceLightMode = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_VoiceInteractionLight_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_onoffcmd", false),
            Member("_voicelightmode", false),
        )

        initproperty()
    }

    var onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd
        get() = _onoffcmd
        set(value){
            _onoffcmd = value
        }

    var voicelightmode: Seres.BCM_eSrv.HPCC_VoiceLightMode
        get() = _voicelightmode
        set(value){
            _voicelightmode = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_VoiceInteractionLight_Ctrl_Enh_In = this): BCM_Service_eSrv_BCM_VoiceInteractionLight_Ctrl_Enh_In{
            this._onoffcmd =  value._onoffcmd
            this._voicelightmode =  value._voicelightmode
            return this
        }

    override fun toString(): String{
        return "$typename(onoffcmd=$onoffcmd, voicelightmode=$voicelightmode)"
    }
}

