package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_AmbientLight_Theme_Enh_In : TypeStruct() {
    private var _theme : Seres.BCM_eSrv.HPCC_AmbientLightTheme = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_AmbientLight_Theme_Enh_In"
        orderedMembers = arrayListOf(
            Member("_theme", false),
        )

        initproperty()
    }

    var theme: Seres.BCM_eSrv.HPCC_AmbientLightTheme
        get() = _theme
        set(value){
            _theme = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_AmbientLight_Theme_Enh_In = this): BCM_Service_eSrv_BCM_AmbientLight_Theme_Enh_In{
            this._theme =  value._theme
            return this
        }

    override fun toString(): String{
        return "$typename(theme=$theme)"
    }
}

