package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_TMS_Climate_Temperature_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.BCM_eSrv.BCM_CallerID = 0u
    private var _zoneid : Int = Seres.BCM_eSrv.ACZoneId.ACZONE_FRONT_LEFT.basevalue
    private var _temperature : Seres.BCM_eSrv.HPCC_Temperature = 0.0f

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_TMS_Climate_Temperature_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_zoneid", false),
            Member("_temperature", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var zoneid: Seres.BCM_eSrv.ACZoneId
        get() = Seres.BCM_eSrv.ACZoneId.values().first { it.basevalue == _zoneid }
        set(value){
            _zoneid = value.basevalue
        }

    var temperature: Seres.BCM_eSrv.HPCC_Temperature
        get() = _temperature
        set(value){
            _temperature = value
        }

    fun copy(value: BCM_Service_eSrv_TMS_Climate_Temperature_Ctrl_Enh_In = this): BCM_Service_eSrv_TMS_Climate_Temperature_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._zoneid =  value._zoneid
            this._temperature =  value._temperature
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, zoneid=$zoneid, temperature=$temperature)"
    }
}

