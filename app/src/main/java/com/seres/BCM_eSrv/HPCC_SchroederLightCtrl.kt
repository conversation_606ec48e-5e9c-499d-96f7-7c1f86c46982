package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class HPCC_SchroederLightCtrl : TypeStruct() {
    private var _brightness : Seres.BCM_eSrv.HPCC_Brightness = 0u
    private var _rgb : Seres.BCM_eSrv.HPCC_RGB = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::HPCC_SchroederLightCtrl"
        orderedMembers = arrayListOf(
            Member("_brightness", false),
            Member("_rgb", false),
        )

        initproperty()
    }

    var brightness: Seres.BCM_eSrv.HPCC_Brightness
        get() = _brightness
        set(value){
            _brightness = value
        }

    var rgb: Seres.BCM_eSrv.HPCC_RGB
        get() = _rgb
        set(value){
            _rgb = value
        }

    fun copy(value: HPCC_SchroederLightCtrl = this): HPCC_SchroederLightCtrl{
            this._brightness =  value._brightness
            this._rgb =  value._rgb
            return this
        }

    override fun toString(): String{
        return "$typename(brightness=$brightness, rgb=$rgb)"
    }
}

