package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_Request : TypeStruct() {
    private var _header : dds.rpc.RequestHeader = dds.rpc.RequestHeader()
    private var _data : Seres.BCM_eSrv.BCM_Service_eSrv_Call = Seres.BCM_eSrv.BCM_Service_eSrv_Call()

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_Request"
        orderedMembers = arrayListOf(
            Member("_header", false),
            Member("_data", false, kclass = _data.ukclass),
        )

        initproperty()
    }

    var header: dds.rpc.RequestHeader
        get() = _header
        set(value){
            _header = value
        }

    var data: Seres.BCM_eSrv.BCM_Service_eSrv_Call
        get() = _data
        set(value){
            _data = value
            updateporperty("_data", _data.ukclass)
        }

    fun copy(value: BCM_Service_eSrv_Request = this): BCM_Service_eSrv_Request{
            this._header =  value._header
            this._data =  value._data
            return this
        }

    override fun toString(): String{
        return "$typename(header=$header, data=$data)"
    }
}

