package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_FrntHatch_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.BCM_eSrv.BCM_CallerID = 0u
    private var _frnhatch_op : Seres.BCM_eSrv.HPCC_LockUnLockPara = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_FrntHatch_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_frnhatch_op", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var frnhatch_op: Seres.BCM_eSrv.HPCC_LockUnLockPara
        get() = _frnhatch_op
        set(value){
            _frnhatch_op = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_FrntHatch_Ctrl_Enh_In = this): BCM_Service_eSrv_BCM_FrntHatch_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._frnhatch_op =  value._frnhatch_op
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, frnhatch_op=$frnhatch_op)"
    }
}

