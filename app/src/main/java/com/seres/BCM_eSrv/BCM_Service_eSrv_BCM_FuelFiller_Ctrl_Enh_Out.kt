package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_FuelFiller_Ctrl_Enh_Out : TypeStruct() {
    private var __default : Byte = 0

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_FuelFiller_Ctrl_Enh_Out"
        orderedMembers = arrayListOf(
            Member("__default", false),
        )

        initproperty()
    }

    var _default: Byte
        get() = __default
        set(value){
            __default = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_FuelFiller_Ctrl_Enh_Out = this): BCM_Service_eSrv_BCM_FuelFiller_Ctrl_Enh_Out{
            this.__default =  value.__default
            return this
        }

    override fun toString(): String{
        return "$typename(_default=$_default)"
    }
}

