package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_TMS_Climate_Level_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.BCM_eSrv.BCM_CallerID = 0u
    private var _climateid : Seres.BCM_eSrv.HPCC_ClimateID = 0u
    private var _acblwlevel : Seres.BCM_eSrv.ACBlwLevel = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_TMS_Climate_Level_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_climateid", false),
            Member("_acblwlevel", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var climateid: Seres.BCM_eSrv.HPCC_ClimateID
        get() = _climateid
        set(value){
            _climateid = value
        }

    var acblwlevel: Seres.BCM_eSrv.ACBlwLevel
        get() = _acblwlevel
        set(value){
            _acblwlevel = value
        }

    fun copy(value: BCM_Service_eSrv_TMS_Climate_Level_Ctrl_Enh_In = this): BCM_Service_eSrv_TMS_Climate_Level_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._climateid =  value._climateid
            this._acblwlevel =  value._acblwlevel
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, climateid=$climateid, acblwlevel=$acblwlevel)"
    }
}

