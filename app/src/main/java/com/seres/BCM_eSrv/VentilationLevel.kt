package Seres.BCM_eSrv

enum class VentilationLevel(var basevalue : Int){
    VentilationLevel_No_Request(0),
    VentilationLevel_OFF(1),
    VentilationLevel_1(2),
    VentilationLevel_2(3),
    VentilationLevel_3(4),
    VentilationLevel_Invalid(255);


    companion object {
        private val valueMap = VentilationLevel.entries.associateBy { it.basevalue }
        fun fromValue(basevalue: Int): VentilationLevel{
            return  valueMap[basevalue]?:VentilationLevel_No_Request
        }    
    }
}
