package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class HPCC_MusicAmbCtrl : TypeStruct() {
    private var _ambzoneid : Seres.BCM_eSrv.HCPP_AmbZoneID = 0u
    private var _onoffcmd : Seres.BCM_eSrv.HPCC_OnOffCmd = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::HPCC_MusicAmbCtrl"
        orderedMembers = arrayListOf(
            Member("_ambzoneid", false),
            Member("_onoffcmd", false),
        )

        initproperty()
    }

    var ambzoneid: Seres.BCM_eSrv.HCPP_AmbZoneID
        get() = _ambzoneid
        set(value){
            _ambzoneid = value
        }

    var onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd
        get() = _onoffcmd
        set(value){
            _onoffcmd = value
        }

    fun copy(value: HPCC_MusicAmbCtrl = this): HPCC_MusicAmbCtrl{
            this._ambzoneid =  value._ambzoneid
            this._onoffcmd =  value._onoffcmd
            return this
        }

    override fun toString(): String{
        return "$typename(ambzoneid=$ambzoneid, onoffcmd=$onoffcmd)"
    }
}

