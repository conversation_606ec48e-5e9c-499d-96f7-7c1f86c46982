package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_Seat_MassageModeCtrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.BCM_eSrv.BCM_CallerID = 0u
    private var _seatid : Seres.BCM_eSrv.HPCC_SeatID = 0u
    private var _massagmode : Int = Seres.BCM_eSrv.MassagMode.MassagMode_No_Request.basevalue

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_Seat_MassageModeCtrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_seatid", false),
            Member("_massagmode", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var seatid: Seres.BCM_eSrv.HPCC_SeatID
        get() = _seatid
        set(value){
            _seatid = value
        }

    var massagmode: Seres.BCM_eSrv.MassagMode
        get() = Seres.BCM_eSrv.MassagMode.values().first { it.basevalue == _massagmode }
        set(value){
            _massagmode = value.basevalue
        }

    fun copy(value: BCM_Service_eSrv_BCM_Seat_MassageModeCtrl_Enh_In = this): BCM_Service_eSrv_BCM_Seat_MassageModeCtrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._seatid =  value._seatid
            this._massagmode =  value._massagmode
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, seatid=$seatid, massagmode=$massagmode)"
    }
}

