package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*
import dds.rpc.*


abstract class BCM_Service_eSrv_base {
    abstract fun BCM_TailGate_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, tailgate_op: Seres.BCM_eSrv.HPCC_TailGate_Op): ReturnCode
    abstract fun BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, maxtargetposition: Seres.BCM_eSrv.HPCC_MaxTargetPosition): ReturnCode
    abstract fun BCM_FrntHatch_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, frnhatch_op: Seres.BCM_eSrv.HPCC_LockUnLockPara): ReturnCode
    abstract fun BCM_ChrgPort_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_FuelFiller_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_SideDoor_ManuaMode_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_SideDoor_MaxTargetPos_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, doorid: Seres.BCM_eSrv.HPCC_DoorID, maxtargetposition: Seres.BCM_eSrv.HPCC_MaxTargetPosition): ReturnCode
    abstract fun BCM_SideDoor_OpenSpeed_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, openspeed: Seres.BCM_eSrv.HPCC_OpenSpeed): ReturnCode
    abstract fun BCM_BrakePadCloseDoorMode_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun TMS_Climate_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, climateid: Seres.BCM_eSrv.HPCC_ClimateID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun TMS_Climate_Auto_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, climateid: Seres.BCM_eSrv.HPCC_ClimateID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun TMS_Climate_Temperature_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, zoneid: Seres.BCM_eSrv.ACZoneId, temperature: Seres.BCM_eSrv.HPCC_Temperature): ReturnCode
    abstract fun TMS_Climate_Temperature_Sync_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun TMS_Climate_Level_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, climateid: Seres.BCM_eSrv.HPCC_ClimateID, acblwlevel: Seres.BCM_eSrv.ACBlwLevel): ReturnCode
    abstract fun TMS_Climate_Mode_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, climateid: Seres.BCM_eSrv.HPCC_ClimateID, climatemode_op: Seres.BCM_eSrv.HPCC_ClimateMode_Op, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun TMS_MaxAsHeat_Status_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, maxacstatus: Seres.BCM_eSrv.HPCC_MaxACStatus, maxheatstatus: Seres.BCM_eSrv.HPCC_MaxHeatStatus): ReturnCode
    abstract fun TMS_ACSwitch_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun TMS_Demist_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, f_or_r: Seres.BCM_eSrv.HPCC_F_Or_R, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun TMS_Auto_Demist_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun TMS_AirPurify_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun TMS_AC_SetBox_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_Auto_Ventilation_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun EMS_RecircleMode_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, recirclemode: Seres.BCM_eSrv.HPCC_RecircleMode): ReturnCode
    abstract fun EMS_SmartZones_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun EMS_LowVoltage_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun EMS_LowVoltage_Energy_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID): ReturnCode
    abstract fun EMS_12V_PowerPort_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_SeatCtrl_Enh(seatposition: Seres.BCM_eSrv.HPCC_SeatPosition): ReturnCode
    abstract fun BCM_Seat_ZeroGravity_Ctrl_Enh(seatid: Seres.BCM_eSrv.HPCC_SeatID, zerogravityparam: Seres.BCM_eSrv.HPCC_ZeroGravityPara): ReturnCode
    abstract fun BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh(seatid: Seres.BCM_eSrv.HPCC_SeatID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_Seat_Fold_Ctrl_Enh(seatid: Seres.BCM_eSrv.HPCC_SeatID, foldpara: Seres.BCM_eSrv.HPCC_FoldPara): ReturnCode
    abstract fun BCM_Seat_HeatLevel_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, seatid: Seres.BCM_eSrv.HPCC_SeatID, heatinglevel: Seres.BCM_eSrv.HeatingLevel): ReturnCode
    abstract fun BCM_Seat_VentilationLevel_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, seatid: Seres.BCM_eSrv.HPCC_SeatID, ventilationlevel: Seres.BCM_eSrv.VentilationLevel): ReturnCode
    abstract fun BCM_Seat_MassageModeCtrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, seatid: Seres.BCM_eSrv.HPCC_SeatID, massagmode: Seres.BCM_eSrv.MassagMode): ReturnCode
    abstract fun BCM_Seat_MassageStrengthCtrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, seatid: Seres.BCM_eSrv.HPCC_SeatID, massagestrength: Seres.BCM_eSrv.MassageStrength): ReturnCode
    abstract fun BCM_Seat_ChildHeatVentilation_Enh(seatid: Seres.BCM_eSrv.HPCC_SeatID, heatonoff: Seres.BCM_eSrv.HPCC_OnOffCmd, ventilationmode: Seres.BCM_eSrv.HPCC_VentilationMode): ReturnCode
    abstract fun BCM_Seat_ChildLeftBehind_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_SunRoof_Ctrl_Enh(sunroofid: Seres.BCM_eSrv.HPCC_SunRoofID, sunroofop: Seres.BCM_eSrv.HPCC_SunRoofOp): ReturnCode
    abstract fun BCM_SunRoof_AUTOLock_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_RoofLight_Ctrl_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_AmbientLight_Theme_Enh(theme: Seres.BCM_eSrv.HPCC_AmbientLightTheme): ReturnCode
    abstract fun BCM_MusicRhyLight_Ctrl_Enh(musicrhythmop: Seres.BCM_eSrv.HPCC_MusicRhythmOp): ReturnCode
    abstract fun BCM_AmbientLight_Brightness_Ctrl_Enh(ambzoneid: Seres.BCM_eSrv.HCPP_AmbZoneID, brightness: Seres.BCM_eSrv.HPCC_Brightness): ReturnCode
    abstract fun BCM_AmbientLight_RGB_Ctrl_Enh(ambzoneid: Seres.BCM_eSrv.HCPP_AmbZoneID, rgb: Seres.BCM_eSrv.HPCC_RGB): ReturnCode
    abstract fun BCM_AmbientLight_Sync_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_WelLight_Ctrl_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_NapMode_Ctrl_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_DOW_BSD_Ctrl_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode
    abstract fun BCM_VoiceInteractionLight_Ctrl_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd, voicelightmode: Seres.BCM_eSrv.HPCC_VoiceLightMode): ReturnCode
}

class BCM_Service_eSrv_dispatcher(requestType: TypeBase, replyType: TypeBase): Dispatcher<BCM_Service_eSrv_base>(requestType, replyType) {
    lateinit var serviceImpl: BCM_Service_eSrv_base
    lateinit var replier: Replier
  
    override fun process(request: TypeBase?) {
        var data: Any? = null
        var sampleIdentity : SampleIdentity = SampleIdentity()
        var desc = (request as BCM_Service_eSrv_Request).data._d
        sampleIdentity = (request as BCM_Service_eSrv_Request).header.requestId
        var result = BCM_Service_eSrv_Reply()
        var header = ReplyHeader()
        header.relatedRequestId = sampleIdentity
        result.header = header
        when (desc) {
            95479469 -> handleReportStatus1(request, result)
            
            35198907 -> handleReportStatus2(request, result)
            
            116988609 -> handleReportStatus3(request, result)
            
            262039799 -> handleReportStatus4(request, result)
            
            24311010 -> handleReportStatus5(request, result)
            
            24647378 -> handleReportStatus6(request, result)
            
            157153507 -> handleReportStatus7(request, result)
            
            220966725 -> handleReportStatus8(request, result)
            
            150775107 -> handleReportStatus9(request, result)
            
            7457417 -> handleReportStatus10(request, result)
            
            113471803 -> handleReportStatus11(request, result)
            
            260335528 -> handleReportStatus12(request, result)
            
            73129101 -> handleReportStatus13(request, result)
            
            198175725 -> handleReportStatus14(request, result)
            
            7549938 -> handleReportStatus15(request, result)
            
            236642961 -> handleReportStatus16(request, result)
            
            57273390 -> handleReportStatus17(request, result)
            
            208944869 -> handleReportStatus18(request, result)
            
            149237749 -> handleReportStatus19(request, result)
            
            1862893 -> handleReportStatus20(request, result)
            
            80205615 -> handleReportStatus21(request, result)
            
            43208485 -> handleReportStatus22(request, result)
            
            33295031 -> handleReportStatus23(request, result)
            
            169684591 -> handleReportStatus24(request, result)
            
            176051844 -> handleReportStatus25(request, result)
            
            241890483 -> handleReportStatus26(request, result)
            
            249557310 -> handleReportStatus27(request, result)
            
            171003682 -> handleReportStatus28(request, result)
            
            255237786 -> handleReportStatus29(request, result)
            
            73410753 -> handleReportStatus30(request, result)
            
            49402652 -> handleReportStatus31(request, result)
            
            170021439 -> handleReportStatus32(request, result)
            
            99281418 -> handleReportStatus33(request, result)
            
            43574639 -> handleReportStatus34(request, result)
            
            208386874 -> handleReportStatus35(request, result)
            
            26589401 -> handleReportStatus36(request, result)
            
            214610333 -> handleReportStatus37(request, result)
            
            3315914 -> handleReportStatus38(request, result)
            
            43250664 -> handleReportStatus39(request, result)
            
            98483052 -> handleReportStatus40(request, result)
            
            215961916 -> handleReportStatus41(request, result)
            
            3900402 -> handleReportStatus42(request, result)
            
            179767365 -> handleReportStatus43(request, result)
            
            94303614 -> handleReportStatus44(request, result)
            
            261679203 -> handleReportStatus45(request, result)
            
            266024731 -> handleReportStatus46(request, result)
            
            132422306 -> handleReportStatus47(request, result)
            
            11398502 -> handleReportStatus48(request, result)
            
            258667097 -> handleReportStatus49(request, result)
        }
    }

    private fun handleReportStatus1(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_95479469).BCM_TailGate_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val tailgate_op = inData.tailgate_op

        val retcode = serviceImpl.BCM_TailGate_Ctrl_Enh(bcm_callerid, tailgate_op)

        var outData = BCM_Service_eSrv_BCM_TailGate_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_95479469()
        data.BCM_TailGate_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus2(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_35198907).BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val maxtargetposition = inData.maxtargetposition

        val retcode = serviceImpl.BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh(bcm_callerid, maxtargetposition)

        var outData = BCM_Service_eSrv_BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_35198907()
        data.BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus3(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_116988609).BCM_FrntHatch_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val frnhatch_op = inData.frnhatch_op

        val retcode = serviceImpl.BCM_FrntHatch_Ctrl_Enh(bcm_callerid, frnhatch_op)

        var outData = BCM_Service_eSrv_BCM_FrntHatch_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_116988609()
        data.BCM_FrntHatch_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus4(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_262039799).BCM_ChrgPort_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_ChrgPort_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_BCM_ChrgPort_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_262039799()
        data.BCM_ChrgPort_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus5(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_24311010).BCM_FuelFiller_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_FuelFiller_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_BCM_FuelFiller_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_24311010()
        data.BCM_FuelFiller_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus6(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_24647378).BCM_SideDoor_ManuaMode_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_SideDoor_ManuaMode_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_BCM_SideDoor_ManuaMode_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_24647378()
        data.BCM_SideDoor_ManuaMode_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus7(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_157153507).BCM_SideDoor_MaxTargetPos_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val doorid = inData.doorid
        val maxtargetposition = inData.maxtargetposition

        val retcode = serviceImpl.BCM_SideDoor_MaxTargetPos_Ctrl_Enh(bcm_callerid, doorid, maxtargetposition)

        var outData = BCM_Service_eSrv_BCM_SideDoor_MaxTargetPos_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_157153507()
        data.BCM_SideDoor_MaxTargetPos_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus8(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_220966725).BCM_SideDoor_OpenSpeed_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val openspeed = inData.openspeed

        val retcode = serviceImpl.BCM_SideDoor_OpenSpeed_Ctrl_Enh(bcm_callerid, openspeed)

        var outData = BCM_Service_eSrv_BCM_SideDoor_OpenSpeed_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_220966725()
        data.BCM_SideDoor_OpenSpeed_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus9(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_150775107).BCM_BrakePadCloseDoorMode_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_BrakePadCloseDoorMode_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_BCM_BrakePadCloseDoorMode_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_150775107()
        data.BCM_BrakePadCloseDoorMode_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus10(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_7457417).TMS_Climate_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val climateid = inData.climateid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.TMS_Climate_Ctrl_Enh(bcm_callerid, climateid, onoffcmd)

        var outData = BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_7457417()
        data.TMS_Climate_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus11(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_113471803).TMS_Climate_Auto_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val climateid = inData.climateid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.TMS_Climate_Auto_Ctrl_Enh(bcm_callerid, climateid, onoffcmd)

        var outData = BCM_Service_eSrv_TMS_Climate_Auto_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_113471803()
        data.TMS_Climate_Auto_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus12(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_260335528).TMS_Climate_Temperature_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val zoneid = inData.zoneid
        val temperature = inData.temperature

        val retcode = serviceImpl.TMS_Climate_Temperature_Ctrl_Enh(bcm_callerid, zoneid, temperature)

        var outData = BCM_Service_eSrv_TMS_Climate_Temperature_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_260335528()
        data.TMS_Climate_Temperature_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus13(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_73129101).TMS_Climate_Temperature_Sync_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.TMS_Climate_Temperature_Sync_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_TMS_Climate_Temperature_Sync_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_73129101()
        data.TMS_Climate_Temperature_Sync_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus14(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_198175725).TMS_Climate_Level_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val climateid = inData.climateid
        val acblwlevel = inData.acblwlevel

        val retcode = serviceImpl.TMS_Climate_Level_Ctrl_Enh(bcm_callerid, climateid, acblwlevel)

        var outData = BCM_Service_eSrv_TMS_Climate_Level_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_198175725()
        data.TMS_Climate_Level_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus15(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_7549938).TMS_Climate_Mode_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val climateid = inData.climateid
        val climatemode_op = inData.climatemode_op
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.TMS_Climate_Mode_Ctrl_Enh(bcm_callerid, climateid, climatemode_op, onoffcmd)

        var outData = BCM_Service_eSrv_TMS_Climate_Mode_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_7549938()
        data.TMS_Climate_Mode_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus16(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_236642961).TMS_MaxAsHeat_Status_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val maxacstatus = inData.maxacstatus
        val maxheatstatus = inData.maxheatstatus

        val retcode = serviceImpl.TMS_MaxAsHeat_Status_Ctrl_Enh(bcm_callerid, maxacstatus, maxheatstatus)

        var outData = BCM_Service_eSrv_TMS_MaxAsHeat_Status_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_236642961()
        data.TMS_MaxAsHeat_Status_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus17(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_57273390).TMS_ACSwitch_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.TMS_ACSwitch_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_TMS_ACSwitch_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_57273390()
        data.TMS_ACSwitch_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus18(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_208944869).TMS_Demist_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val f_or_r = inData.f_or_r
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.TMS_Demist_Ctrl_Enh(bcm_callerid, f_or_r, onoffcmd)

        var outData = BCM_Service_eSrv_TMS_Demist_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_208944869()
        data.TMS_Demist_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus19(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_149237749).TMS_Auto_Demist_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.TMS_Auto_Demist_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_TMS_Auto_Demist_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_149237749()
        data.TMS_Auto_Demist_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus20(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_1862893).TMS_AirPurify_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.TMS_AirPurify_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_TMS_AirPurify_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_1862893()
        data.TMS_AirPurify_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus21(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_80205615).TMS_AC_SetBox_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.TMS_AC_SetBox_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_TMS_AC_SetBox_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_80205615()
        data.TMS_AC_SetBox_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus22(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_43208485).BCM_Auto_Ventilation_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_Auto_Ventilation_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_BCM_Auto_Ventilation_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_43208485()
        data.BCM_Auto_Ventilation_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus23(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_33295031).EMS_RecircleMode_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val recirclemode = inData.recirclemode

        val retcode = serviceImpl.EMS_RecircleMode_Ctrl_Enh(bcm_callerid, recirclemode)

        var outData = BCM_Service_eSrv_EMS_RecircleMode_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_33295031()
        data.EMS_RecircleMode_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus24(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_169684591).EMS_SmartZones_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.EMS_SmartZones_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_EMS_SmartZones_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_169684591()
        data.EMS_SmartZones_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus25(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_176051844).EMS_LowVoltage_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.EMS_LowVoltage_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_EMS_LowVoltage_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_176051844()
        data.EMS_LowVoltage_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus26(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_241890483).EMS_LowVoltage_Energy_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid

        val retcode = serviceImpl.EMS_LowVoltage_Energy_Ctrl_Enh(bcm_callerid)

        var outData = BCM_Service_eSrv_EMS_LowVoltage_Energy_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_241890483()
        data.EMS_LowVoltage_Energy_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus27(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_249557310).EMS_12V_PowerPort_Ctrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.EMS_12V_PowerPort_Ctrl_Enh(bcm_callerid, onoffcmd)

        var outData = BCM_Service_eSrv_EMS_12V_PowerPort_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_249557310()
        data.EMS_12V_PowerPort_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus28(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_171003682).BCM_SeatCtrl_EnhIn
        val seatposition = inData.seatposition

        val retcode = serviceImpl.BCM_SeatCtrl_Enh(seatposition)

        var outData = BCM_Service_eSrv_BCM_SeatCtrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_171003682()
        data.BCM_SeatCtrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus29(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_255237786).BCM_Seat_ZeroGravity_Ctrl_EnhIn
        val seatid = inData.seatid
        val zerogravityparam = inData.zerogravityparam

        val retcode = serviceImpl.BCM_Seat_ZeroGravity_Ctrl_Enh(seatid, zerogravityparam)

        var outData = BCM_Service_eSrv_BCM_Seat_ZeroGravity_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_255237786()
        data.BCM_Seat_ZeroGravity_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus30(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_73410753).BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhIn
        val seatid = inData.seatid
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh(seatid, onoffcmd)

        var outData = BCM_Service_eSrv_BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_73410753()
        data.BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus31(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_49402652).BCM_Seat_Fold_Ctrl_EnhIn
        val seatid = inData.seatid
        val foldpara = inData.foldpara

        val retcode = serviceImpl.BCM_Seat_Fold_Ctrl_Enh(seatid, foldpara)

        var outData = BCM_Service_eSrv_BCM_Seat_Fold_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_49402652()
        data.BCM_Seat_Fold_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus32(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_170021439).BCM_Seat_HeatLevel_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val seatid = inData.seatid
        val heatinglevel = inData.heatinglevel

        val retcode = serviceImpl.BCM_Seat_HeatLevel_Enh(bcm_callerid, seatid, heatinglevel)

        var outData = BCM_Service_eSrv_BCM_Seat_HeatLevel_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_170021439()
        data.BCM_Seat_HeatLevel_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus33(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_99281418).BCM_Seat_VentilationLevel_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val seatid = inData.seatid
        val ventilationlevel = inData.ventilationlevel

        val retcode = serviceImpl.BCM_Seat_VentilationLevel_Enh(bcm_callerid, seatid, ventilationlevel)

        var outData = BCM_Service_eSrv_BCM_Seat_VentilationLevel_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_99281418()
        data.BCM_Seat_VentilationLevel_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus34(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_43574639).BCM_Seat_MassageModeCtrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val seatid = inData.seatid
        val massagmode = inData.massagmode

        val retcode = serviceImpl.BCM_Seat_MassageModeCtrl_Enh(bcm_callerid, seatid, massagmode)

        var outData = BCM_Service_eSrv_BCM_Seat_MassageModeCtrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_43574639()
        data.BCM_Seat_MassageModeCtrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus35(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_208386874).BCM_Seat_MassageStrengthCtrl_EnhIn
        val bcm_callerid = inData.bcm_callerid
        val seatid = inData.seatid
        val massagestrength = inData.massagestrength

        val retcode = serviceImpl.BCM_Seat_MassageStrengthCtrl_Enh(bcm_callerid, seatid, massagestrength)

        var outData = BCM_Service_eSrv_BCM_Seat_MassageStrengthCtrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_208386874()
        data.BCM_Seat_MassageStrengthCtrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus36(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_26589401).BCM_Seat_ChildHeatVentilation_EnhIn
        val seatid = inData.seatid
        val heatonoff = inData.heatonoff
        val ventilationmode = inData.ventilationmode

        val retcode = serviceImpl.BCM_Seat_ChildHeatVentilation_Enh(seatid, heatonoff, ventilationmode)

        var outData = BCM_Service_eSrv_BCM_Seat_ChildHeatVentilation_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_26589401()
        data.BCM_Seat_ChildHeatVentilation_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus37(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_214610333).BCM_Seat_ChildLeftBehind_EnhIn
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_Seat_ChildLeftBehind_Enh(onoffcmd)

        var outData = BCM_Service_eSrv_BCM_Seat_ChildLeftBehind_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_214610333()
        data.BCM_Seat_ChildLeftBehind_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus38(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_3315914).BCM_SunRoof_Ctrl_EnhIn
        val sunroofid = inData.sunroofid
        val sunroofop = inData.sunroofop

        val retcode = serviceImpl.BCM_SunRoof_Ctrl_Enh(sunroofid, sunroofop)

        var outData = BCM_Service_eSrv_BCM_SunRoof_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_3315914()
        data.BCM_SunRoof_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus39(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_43250664).BCM_SunRoof_AUTOLock_EnhIn
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_SunRoof_AUTOLock_Enh(onoffcmd)

        var outData = BCM_Service_eSrv_BCM_SunRoof_AUTOLock_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_43250664()
        data.BCM_SunRoof_AUTOLock_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus40(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_98483052).BCM_RoofLight_Ctrl_EnhIn
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_RoofLight_Ctrl_Enh(onoffcmd)

        var outData = BCM_Service_eSrv_BCM_RoofLight_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_98483052()
        data.BCM_RoofLight_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus41(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_215961916).BCM_AmbientLight_Theme_EnhIn
        val theme = inData.theme

        val retcode = serviceImpl.BCM_AmbientLight_Theme_Enh(theme)

        var outData = BCM_Service_eSrv_BCM_AmbientLight_Theme_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_215961916()
        data.BCM_AmbientLight_Theme_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus42(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_3900402).BCM_MusicRhyLight_Ctrl_EnhIn
        val musicrhythmop = inData.musicrhythmop

        val retcode = serviceImpl.BCM_MusicRhyLight_Ctrl_Enh(musicrhythmop)

        var outData = BCM_Service_eSrv_BCM_MusicRhyLight_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_3900402()
        data.BCM_MusicRhyLight_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus43(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_179767365).BCM_AmbientLight_Brightness_Ctrl_EnhIn
        val ambzoneid = inData.ambzoneid
        val brightness = inData.brightness

        val retcode = serviceImpl.BCM_AmbientLight_Brightness_Ctrl_Enh(ambzoneid, brightness)

        var outData = BCM_Service_eSrv_BCM_AmbientLight_Brightness_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_179767365()
        data.BCM_AmbientLight_Brightness_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus44(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_94303614).BCM_AmbientLight_RGB_Ctrl_EnhIn
        val ambzoneid = inData.ambzoneid
        val rgb = inData.rgb

        val retcode = serviceImpl.BCM_AmbientLight_RGB_Ctrl_Enh(ambzoneid, rgb)

        var outData = BCM_Service_eSrv_BCM_AmbientLight_RGB_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_94303614()
        data.BCM_AmbientLight_RGB_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus45(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_261679203).BCM_AmbientLight_Sync_EnhIn
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_AmbientLight_Sync_Enh(onoffcmd)

        var outData = BCM_Service_eSrv_BCM_AmbientLight_Sync_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_261679203()
        data.BCM_AmbientLight_Sync_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus46(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_266024731).BCM_WelLight_Ctrl_EnhIn
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_WelLight_Ctrl_Enh(onoffcmd)

        var outData = BCM_Service_eSrv_BCM_WelLight_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_266024731()
        data.BCM_WelLight_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus47(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_132422306).BCM_NapMode_Ctrl_EnhIn
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_NapMode_Ctrl_Enh(onoffcmd)

        var outData = BCM_Service_eSrv_BCM_NapMode_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_132422306()
        data.BCM_NapMode_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus48(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_11398502).BCM_DOW_BSD_Ctrl_EnhIn
        val onoffcmd = inData.onoffcmd

        val retcode = serviceImpl.BCM_DOW_BSD_Ctrl_Enh(onoffcmd)

        var outData = BCM_Service_eSrv_BCM_DOW_BSD_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_11398502()
        data.BCM_DOW_BSD_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
            
    private fun handleReportStatus49(request: BCM_Service_eSrv_Request, result: BCM_Service_eSrv_Reply) {
        val inData = ((request as BCM_Service_eSrv_Request).data as BCM_Service_eSrv_Call.descriptor_258667097).BCM_VoiceInteractionLight_Ctrl_EnhIn
        val onoffcmd = inData.onoffcmd
        val voicelightmode = inData.voicelightmode

        val retcode = serviceImpl.BCM_VoiceInteractionLight_Ctrl_Enh(onoffcmd, voicelightmode)

        var outData = BCM_Service_eSrv_BCM_VoiceInteractionLight_Ctrl_Enh_Result()
        outData._return = retcode

        var data = BCM_Service_eSrv_Return.descriptor_258667097()
        data.BCM_VoiceInteractionLight_Ctrl_EnhResult = outData
        result.data = data
        replier.send_reply(result)
    }
    override fun add_service_impl(serviceImpl: BCM_Service_eSrv_base) {
        this.serviceImpl = serviceImpl
    }  

    override fun set_replier(replier: Replier) {
        this.replier = replier
    }  
}

class BCM_Service_eSrvService(
    serviceImpl: BCM_Service_eSrv_base,
    server: Server,
    param: ServiceParam,
    dispatcher: Dispatcher<BCM_Service_eSrv_base> = BCM_Service_eSrv_dispatcher(BCM_Service_eSrv_Request(), BCM_Service_eSrv_Reply())
    ) : ServiceEndpoint<BCM_Service_eSrv_base>(serviceImpl, param, server, dispatcher) {
}
