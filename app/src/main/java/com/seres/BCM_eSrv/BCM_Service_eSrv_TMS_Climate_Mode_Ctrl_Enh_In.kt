package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_TMS_Climate_Mode_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.BCM_eSrv.BCM_CallerID = 0u
    private var _climateid : Seres.BCM_eSrv.HPCC_ClimateID = 0u
    private var _climatemode_op : Seres.BCM_eSrv.HPCC_ClimateMode_Op = 0u
    private var _onoffcmd : Seres.BCM_eSrv.HPCC_OnOffCmd = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_TMS_Climate_Mode_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_climateid", false),
            Member("_climatemode_op", false),
            Member("_onoffcmd", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var climateid: Seres.BCM_eSrv.HPCC_ClimateID
        get() = _climateid
        set(value){
            _climateid = value
        }

    var climatemode_op: Seres.BCM_eSrv.HPCC_ClimateMode_Op
        get() = _climatemode_op
        set(value){
            _climatemode_op = value
        }

    var onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd
        get() = _onoffcmd
        set(value){
            _onoffcmd = value
        }

    fun copy(value: BCM_Service_eSrv_TMS_Climate_Mode_Ctrl_Enh_In = this): BCM_Service_eSrv_TMS_Climate_Mode_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._climateid =  value._climateid
            this._climatemode_op =  value._climatemode_op
            this._onoffcmd =  value._onoffcmd
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, climateid=$climateid, climatemode_op=$climatemode_op, onoffcmd=$onoffcmd)"
    }
}

