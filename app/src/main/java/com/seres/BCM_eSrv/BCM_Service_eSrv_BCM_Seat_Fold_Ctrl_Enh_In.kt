package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_Seat_Fold_Ctrl_Enh_In : TypeStruct() {
    private var _seatid : Seres.BCM_eSrv.HPCC_SeatID = 0u
    private var _foldpara : Seres.BCM_eSrv.HPCC_FoldPara = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_Seat_Fold_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_seatid", false),
            Member("_foldpara", false),
        )

        initproperty()
    }

    var seatid: Seres.BCM_eSrv.HPCC_SeatID
        get() = _seatid
        set(value){
            _seatid = value
        }

    var foldpara: Seres.BCM_eSrv.HPCC_FoldPara
        get() = _foldpara
        set(value){
            _foldpara = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_Seat_Fold_Ctrl_Enh_In = this): BCM_Service_eSrv_BCM_Seat_Fold_Ctrl_Enh_In{
            this._seatid =  value._seatid
            this._foldpara =  value._foldpara
            return this
        }

    override fun toString(): String{
        return "$typename(seatid=$seatid, foldpara=$foldpara)"
    }
}

