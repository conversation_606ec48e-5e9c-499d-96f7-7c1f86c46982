package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_Result : TypeStruct() {
    private var _TMS_Climate_Ctrl_EnhOut : Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_Out = Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_Out()
    private var __return : Seres.BCM_eSrv.ReturnCode = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_Result"
        orderedMembers = arrayListOf(
            Member("_TMS_Climate_Ctrl_EnhOut", false),
            Member("__return", false),
        )

        initproperty()
    }

    var TMS_Climate_Ctrl_EnhOut: Seres.BCM_eSrv.BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_Out
        get() = _TMS_Climate_Ctrl_EnhOut
        set(value){
            _TMS_Climate_Ctrl_EnhOut = value
        }

    var _return: Seres.BCM_eSrv.ReturnCode
        get() = __return
        set(value){
            __return = value
        }

    fun copy(value: BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_Result = this): BCM_Service_eSrv_TMS_Climate_Ctrl_Enh_Result{
            this._TMS_Climate_Ctrl_EnhOut =  value._TMS_Climate_Ctrl_EnhOut
            this.__return =  value.__return
            return this
        }

    override fun toString(): String{
        return "$typename(TMS_Climate_Ctrl_EnhOut=$TMS_Climate_Ctrl_EnhOut, _return=$_return)"
    }
}

