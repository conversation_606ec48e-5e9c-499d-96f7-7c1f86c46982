package Seres.BCM_eSrv

enum class MassageStrength(var basevalue : Int){
    MassageStrength_No_Request(0),
    MassageStrength_OFF(1),
    MassageStrength_SOFT(2),
    MassageStrength_MEDIUM(3),
    MassageStrength_HEAVY(4),
    MassageStrength_INVALID(255);


    companion object {
        private val valueMap = MassageStrength.entries.associateBy { it.basevalue }
        fun fromValue(basevalue: Int): MassageStrength{
            return  valueMap[basevalue]?:MassageStrength_No_Request
        }    
    }
}
