package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*
import com.seres.dds.server.api.HPCC_SeatPosition
import dds.rpc.*


class BCM_Service_eSrvClient : ClientEndpoint {
    constructor(
        param: ClientParam,
        rpcRequest: TypeBase = BCM_Service_eSrv_Request(),
        rpcReply: TypeBase = BCM_Service_eSrv_Reply()
    ) : super(param, rpcRequest, rpcReply)

    fun BCM_TailGate_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, tailgate_op: Seres.BCM_eSrv.HPCC_TailGate_Op): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_95479469().apply{
            BCM_TailGate_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_TailGate_Ctrl_EnhIn.tailgate_op = tailgate_op as Seres.BCM_eSrv.HPCC_TailGate_Op
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 95479469) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_95479469
                return return_val.BCM_TailGate_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_TailGate_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, maxtargetposition: Seres.BCM_eSrv.HPCC_MaxTargetPosition): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_35198907().apply{
            BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhIn.maxtargetposition = maxtargetposition as Seres.BCM_eSrv.HPCC_MaxTargetPosition
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 35198907) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_35198907
                return return_val.BCM_TailGate_SetMaxTargetPosition_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_FrntHatch_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, frnhatch_op: Seres.BCM_eSrv.HPCC_LockUnLockPara): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_116988609().apply{
            BCM_FrntHatch_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_FrntHatch_Ctrl_EnhIn.frnhatch_op = frnhatch_op as Seres.BCM_eSrv.HPCC_LockUnLockPara
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 116988609) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_116988609
                return return_val.BCM_FrntHatch_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_FrntHatch_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_ChrgPort_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_262039799().apply{
            BCM_ChrgPort_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_ChrgPort_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 262039799) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_262039799
                return return_val.BCM_ChrgPort_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_ChrgPort_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_FuelFiller_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_24311010().apply{
            BCM_FuelFiller_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_FuelFiller_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 24311010) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_24311010
                return return_val.BCM_FuelFiller_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_FuelFiller_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_SideDoor_ManuaMode_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_24647378().apply{
            BCM_SideDoor_ManuaMode_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_SideDoor_ManuaMode_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 24647378) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_24647378
                return return_val.BCM_SideDoor_ManuaMode_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_SideDoor_ManuaMode_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_SideDoor_MaxTargetPos_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, doorid: Seres.BCM_eSrv.HPCC_DoorID, maxtargetposition: Seres.BCM_eSrv.HPCC_MaxTargetPosition): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_157153507().apply{
            BCM_SideDoor_MaxTargetPos_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_SideDoor_MaxTargetPos_Ctrl_EnhIn.doorid = doorid as Seres.BCM_eSrv.HPCC_DoorID
            BCM_SideDoor_MaxTargetPos_Ctrl_EnhIn.maxtargetposition = maxtargetposition as Seres.BCM_eSrv.HPCC_MaxTargetPosition
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 157153507) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_157153507
                return return_val.BCM_SideDoor_MaxTargetPos_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_SideDoor_MaxTargetPos_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_SideDoor_OpenSpeed_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, openspeed: Seres.BCM_eSrv.HPCC_OpenSpeed): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_220966725().apply{
            BCM_SideDoor_OpenSpeed_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_SideDoor_OpenSpeed_Ctrl_EnhIn.openspeed = openspeed as Seres.BCM_eSrv.HPCC_OpenSpeed
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 220966725) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_220966725
                return return_val.BCM_SideDoor_OpenSpeed_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_SideDoor_OpenSpeed_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_BrakePadCloseDoorMode_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_150775107().apply{
            BCM_BrakePadCloseDoorMode_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_BrakePadCloseDoorMode_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 150775107) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_150775107
                return return_val.BCM_BrakePadCloseDoorMode_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_BrakePadCloseDoorMode_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_Climate_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, climateid: Seres.BCM_eSrv.HPCC_ClimateID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_7457417().apply{
            TMS_Climate_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_Climate_Ctrl_EnhIn.climateid = climateid as Seres.BCM_eSrv.HPCC_ClimateID
            TMS_Climate_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 7457417) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_7457417
                return return_val.TMS_Climate_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_Climate_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_Climate_Auto_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, climateid: Seres.BCM_eSrv.HPCC_ClimateID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_113471803().apply{
            TMS_Climate_Auto_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_Climate_Auto_Ctrl_EnhIn.climateid = climateid as Seres.BCM_eSrv.HPCC_ClimateID
            TMS_Climate_Auto_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 113471803) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_113471803
                return return_val.TMS_Climate_Auto_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_Climate_Auto_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_Climate_Temperature_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, zoneid: Seres.BCM_eSrv.ACZoneId, temperature: Seres.BCM_eSrv.HPCC_Temperature): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_260335528().apply{
            TMS_Climate_Temperature_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_Climate_Temperature_Ctrl_EnhIn.zoneid = zoneid as Seres.BCM_eSrv.ACZoneId
            TMS_Climate_Temperature_Ctrl_EnhIn.temperature = temperature as Seres.BCM_eSrv.HPCC_Temperature
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 260335528) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_260335528
                return return_val.TMS_Climate_Temperature_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_Climate_Temperature_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_Climate_Temperature_Sync_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_73129101().apply{
            TMS_Climate_Temperature_Sync_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_Climate_Temperature_Sync_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 73129101) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_73129101
                return return_val.TMS_Climate_Temperature_Sync_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_Climate_Temperature_Sync_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_Climate_Level_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, climateid: Seres.BCM_eSrv.HPCC_ClimateID, acblwlevel: Seres.BCM_eSrv.ACBlwLevel): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_198175725().apply{
            TMS_Climate_Level_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_Climate_Level_Ctrl_EnhIn.climateid = climateid as Seres.BCM_eSrv.HPCC_ClimateID
            TMS_Climate_Level_Ctrl_EnhIn.acblwlevel = acblwlevel as Seres.BCM_eSrv.ACBlwLevel
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 198175725) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_198175725
                return return_val.TMS_Climate_Level_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_Climate_Level_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_Climate_Mode_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, climateid: Seres.BCM_eSrv.HPCC_ClimateID, climatemode_op: Seres.BCM_eSrv.HPCC_ClimateMode_Op, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_7549938().apply{
            TMS_Climate_Mode_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_Climate_Mode_Ctrl_EnhIn.climateid = climateid as Seres.BCM_eSrv.HPCC_ClimateID
            TMS_Climate_Mode_Ctrl_EnhIn.climatemode_op = climatemode_op as Seres.BCM_eSrv.HPCC_ClimateMode_Op
            TMS_Climate_Mode_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 7549938) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_7549938
                return return_val.TMS_Climate_Mode_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_Climate_Mode_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_MaxAsHeat_Status_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, maxacstatus: Seres.BCM_eSrv.HPCC_MaxACStatus, maxheatstatus: Seres.BCM_eSrv.HPCC_MaxHeatStatus): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_236642961().apply{
            TMS_MaxAsHeat_Status_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_MaxAsHeat_Status_Ctrl_EnhIn.maxacstatus = maxacstatus as Seres.BCM_eSrv.HPCC_MaxACStatus
            TMS_MaxAsHeat_Status_Ctrl_EnhIn.maxheatstatus = maxheatstatus as Seres.BCM_eSrv.HPCC_MaxHeatStatus
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 236642961) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_236642961
                return return_val.TMS_MaxAsHeat_Status_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_MaxAsHeat_Status_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_ACSwitch_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_57273390().apply{
            TMS_ACSwitch_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_ACSwitch_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 57273390) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_57273390
                return return_val.TMS_ACSwitch_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_ACSwitch_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_Demist_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, f_or_r: Seres.BCM_eSrv.HPCC_F_Or_R, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_208944869().apply{
            TMS_Demist_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_Demist_Ctrl_EnhIn.f_or_r = f_or_r as Seres.BCM_eSrv.HPCC_F_Or_R
            TMS_Demist_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 208944869) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_208944869
                return return_val.TMS_Demist_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_Demist_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_Auto_Demist_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_149237749().apply{
            TMS_Auto_Demist_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_Auto_Demist_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 149237749) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_149237749
                return return_val.TMS_Auto_Demist_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_Auto_Demist_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_AirPurify_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_1862893().apply{
            TMS_AirPurify_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_AirPurify_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 1862893) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_1862893
                return return_val.TMS_AirPurify_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_AirPurify_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun TMS_AC_SetBox_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_80205615().apply{
            TMS_AC_SetBox_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            TMS_AC_SetBox_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 80205615) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_80205615
                return return_val.TMS_AC_SetBox_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call TMS_AC_SetBox_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Auto_Ventilation_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_43208485().apply{
            BCM_Auto_Ventilation_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_Auto_Ventilation_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 43208485) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_43208485
                return return_val.BCM_Auto_Ventilation_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_Auto_Ventilation_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun EMS_RecircleMode_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, recirclemode: Seres.BCM_eSrv.HPCC_RecircleMode): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_33295031().apply{
            EMS_RecircleMode_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            EMS_RecircleMode_Ctrl_EnhIn.recirclemode = recirclemode as Seres.BCM_eSrv.HPCC_RecircleMode
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 33295031) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_33295031
                return return_val.EMS_RecircleMode_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call EMS_RecircleMode_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun EMS_SmartZones_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_169684591().apply{
            EMS_SmartZones_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            EMS_SmartZones_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 169684591) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_169684591
                return return_val.EMS_SmartZones_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call EMS_SmartZones_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun EMS_LowVoltage_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_176051844().apply{
            EMS_LowVoltage_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            EMS_LowVoltage_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 176051844) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_176051844
                return return_val.EMS_LowVoltage_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call EMS_LowVoltage_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun EMS_LowVoltage_Energy_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_241890483().apply{
            EMS_LowVoltage_Energy_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 241890483) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_241890483
                return return_val.EMS_LowVoltage_Energy_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call EMS_LowVoltage_Energy_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun EMS_12V_PowerPort_Ctrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_249557310().apply{
            EMS_12V_PowerPort_Ctrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            EMS_12V_PowerPort_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 249557310) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_249557310
                return return_val.EMS_12V_PowerPort_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call EMS_12V_PowerPort_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_SeatCtrl_Enh(seatposition: TypeStruct): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_171003682().apply{
            BCM_SeatCtrl_EnhIn.seatposition = seatposition as Seres.BCM_eSrv.HPCC_SeatPosition
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 171003682) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_171003682
                return return_val.BCM_SeatCtrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_SeatCtrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Seat_ZeroGravity_Ctrl_Enh(seatid: Seres.BCM_eSrv.HPCC_SeatID, zerogravityparam: Seres.BCM_eSrv.HPCC_ZeroGravityPara): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_255237786().apply{
            BCM_Seat_ZeroGravity_Ctrl_EnhIn.seatid = seatid as Seres.BCM_eSrv.HPCC_SeatID
            BCM_Seat_ZeroGravity_Ctrl_EnhIn.zerogravityparam = zerogravityparam as Seres.BCM_eSrv.HPCC_ZeroGravityPara
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 255237786) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_255237786
                return return_val.BCM_Seat_ZeroGravity_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_Seat_ZeroGravity_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh(seatid: Seres.BCM_eSrv.HPCC_SeatID, onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_73410753().apply{
            BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhIn.seatid = seatid as Seres.BCM_eSrv.HPCC_SeatID
            BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 73410753) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_73410753
                return return_val.BCM_Seat_ZeroGravity_ChildLock_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Seat_Fold_Ctrl_Enh(seatid: Seres.BCM_eSrv.HPCC_SeatID, foldpara: Seres.BCM_eSrv.HPCC_FoldPara): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_49402652().apply{
            BCM_Seat_Fold_Ctrl_EnhIn.seatid = seatid as Seres.BCM_eSrv.HPCC_SeatID
            BCM_Seat_Fold_Ctrl_EnhIn.foldpara = foldpara as Seres.BCM_eSrv.HPCC_FoldPara
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 49402652) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_49402652
                return return_val.BCM_Seat_Fold_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_Seat_Fold_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Seat_HeatLevel_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, seatid: Seres.BCM_eSrv.HPCC_SeatID, heatinglevel: Seres.BCM_eSrv.HeatingLevel): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_170021439().apply{
            BCM_Seat_HeatLevel_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_Seat_HeatLevel_EnhIn.seatid = seatid as Seres.BCM_eSrv.HPCC_SeatID
            BCM_Seat_HeatLevel_EnhIn.heatinglevel = heatinglevel as Seres.BCM_eSrv.HeatingLevel
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 170021439) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_170021439
                return return_val.BCM_Seat_HeatLevel_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_Seat_HeatLevel_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Seat_VentilationLevel_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, seatid: Seres.BCM_eSrv.HPCC_SeatID, ventilationlevel: Seres.BCM_eSrv.VentilationLevel): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_99281418().apply{
            BCM_Seat_VentilationLevel_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_Seat_VentilationLevel_EnhIn.seatid = seatid as Seres.BCM_eSrv.HPCC_SeatID
            BCM_Seat_VentilationLevel_EnhIn.ventilationlevel = ventilationlevel as Seres.BCM_eSrv.VentilationLevel
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 99281418) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_99281418
                return return_val.BCM_Seat_VentilationLevel_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_Seat_VentilationLevel_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Seat_MassageModeCtrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, seatid: Seres.BCM_eSrv.HPCC_SeatID, massagmode: Seres.BCM_eSrv.MassagMode): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_43574639().apply{
            BCM_Seat_MassageModeCtrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_Seat_MassageModeCtrl_EnhIn.seatid = seatid as Seres.BCM_eSrv.HPCC_SeatID
            BCM_Seat_MassageModeCtrl_EnhIn.massagmode = massagmode as Seres.BCM_eSrv.MassagMode
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 43574639) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_43574639
                return return_val.BCM_Seat_MassageModeCtrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_Seat_MassageModeCtrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Seat_MassageStrengthCtrl_Enh(bcm_callerid: Seres.BCM_eSrv.BCM_CallerID, seatid: Seres.BCM_eSrv.HPCC_SeatID, massagestrength: Seres.BCM_eSrv.MassageStrength): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_208386874().apply{
            BCM_Seat_MassageStrengthCtrl_EnhIn.bcm_callerid = bcm_callerid as Seres.BCM_eSrv.BCM_CallerID
            BCM_Seat_MassageStrengthCtrl_EnhIn.seatid = seatid as Seres.BCM_eSrv.HPCC_SeatID
            BCM_Seat_MassageStrengthCtrl_EnhIn.massagestrength = massagestrength as Seres.BCM_eSrv.MassageStrength
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 208386874) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_208386874
                return return_val.BCM_Seat_MassageStrengthCtrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_Seat_MassageStrengthCtrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Seat_ChildHeatVentilation_Enh(seatid: Seres.BCM_eSrv.HPCC_SeatID, heatonoff: Seres.BCM_eSrv.HPCC_OnOffCmd, ventilationmode: Seres.BCM_eSrv.HPCC_VentilationMode): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_26589401().apply{
            BCM_Seat_ChildHeatVentilation_EnhIn.seatid = seatid as Seres.BCM_eSrv.HPCC_SeatID
            BCM_Seat_ChildHeatVentilation_EnhIn.heatonoff = heatonoff as Seres.BCM_eSrv.HPCC_OnOffCmd
            BCM_Seat_ChildHeatVentilation_EnhIn.ventilationmode = ventilationmode as Seres.BCM_eSrv.HPCC_VentilationMode
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 26589401) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_26589401
                return return_val.BCM_Seat_ChildHeatVentilation_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_Seat_ChildHeatVentilation_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_Seat_ChildLeftBehind_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_214610333().apply{
            BCM_Seat_ChildLeftBehind_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 214610333) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_214610333
                return return_val.BCM_Seat_ChildLeftBehind_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_Seat_ChildLeftBehind_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_SunRoof_Ctrl_Enh(sunroofid: Seres.BCM_eSrv.HPCC_SunRoofID, sunroofop: Seres.BCM_eSrv.HPCC_SunRoofOp): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_3315914().apply{
            BCM_SunRoof_Ctrl_EnhIn.sunroofid = sunroofid as Seres.BCM_eSrv.HPCC_SunRoofID
            BCM_SunRoof_Ctrl_EnhIn.sunroofop = sunroofop as Seres.BCM_eSrv.HPCC_SunRoofOp
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 3315914) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_3315914
                return return_val.BCM_SunRoof_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_SunRoof_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_SunRoof_AUTOLock_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_43250664().apply{
            BCM_SunRoof_AUTOLock_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 43250664) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_43250664
                return return_val.BCM_SunRoof_AUTOLock_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_SunRoof_AUTOLock_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_RoofLight_Ctrl_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_98483052().apply{
            BCM_RoofLight_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 98483052) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_98483052
                return return_val.BCM_RoofLight_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_RoofLight_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_AmbientLight_Theme_Enh(theme: Seres.BCM_eSrv.HPCC_AmbientLightTheme): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_215961916().apply{
            BCM_AmbientLight_Theme_EnhIn.theme = theme as Seres.BCM_eSrv.HPCC_AmbientLightTheme
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 215961916) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_215961916
                return return_val.BCM_AmbientLight_Theme_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_AmbientLight_Theme_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_MusicRhyLight_Ctrl_Enh(musicrhythmop: TypeStruct): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_3900402().apply{
            BCM_MusicRhyLight_Ctrl_EnhIn.musicrhythmop = musicrhythmop as Seres.BCM_eSrv.HPCC_MusicRhythmOp
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 3900402) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_3900402
                return return_val.BCM_MusicRhyLight_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_MusicRhyLight_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_AmbientLight_Brightness_Ctrl_Enh(ambzoneid: Seres.BCM_eSrv.HCPP_AmbZoneID, brightness: Seres.BCM_eSrv.HPCC_Brightness): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_179767365().apply{
            BCM_AmbientLight_Brightness_Ctrl_EnhIn.ambzoneid = ambzoneid as Seres.BCM_eSrv.HCPP_AmbZoneID
            BCM_AmbientLight_Brightness_Ctrl_EnhIn.brightness = brightness as Seres.BCM_eSrv.HPCC_Brightness
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 179767365) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_179767365
                return return_val.BCM_AmbientLight_Brightness_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_AmbientLight_Brightness_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_AmbientLight_RGB_Ctrl_Enh(ambzoneid: Seres.BCM_eSrv.HCPP_AmbZoneID, rgb: Seres.BCM_eSrv.HPCC_RGB): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_94303614().apply{
            BCM_AmbientLight_RGB_Ctrl_EnhIn.ambzoneid = ambzoneid as Seres.BCM_eSrv.HCPP_AmbZoneID
            BCM_AmbientLight_RGB_Ctrl_EnhIn.rgb = rgb as Seres.BCM_eSrv.HPCC_RGB
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 94303614) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_94303614
                return return_val.BCM_AmbientLight_RGB_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_AmbientLight_RGB_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_AmbientLight_Sync_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_261679203().apply{
            BCM_AmbientLight_Sync_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 261679203) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_261679203
                return return_val.BCM_AmbientLight_Sync_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_AmbientLight_Sync_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_WelLight_Ctrl_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_266024731().apply{
            BCM_WelLight_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 266024731) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_266024731
                return return_val.BCM_WelLight_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_WelLight_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_NapMode_Ctrl_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_132422306().apply{
            BCM_NapMode_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 132422306) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_132422306
                return return_val.BCM_NapMode_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_NapMode_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_DOW_BSD_Ctrl_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_11398502().apply{
            BCM_DOW_BSD_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 11398502) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_11398502
                return return_val.BCM_DOW_BSD_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_DOW_BSD_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }

    fun BCM_VoiceInteractionLight_Ctrl_Enh(onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd, voicelightmode: Seres.BCM_eSrv.HPCC_VoiceLightMode): ReturnCode{
        var ret : ReturnCode = 0.toUByte()
        var header = RequestHeader()
        var sampleIdentity = SampleIdentity()
        var guid = GUID_t()
        var entityId = EntityId_t()

        var seq = get_sequenceNumber()
        sampleIdentity.sequence_number.low = (seq and 0xFFFFuL).toUInt()
        sampleIdentity.sequence_number.high = ((seq and 0xFFFF0000uL).shr(16).toInt())

        guid.guidPrefix = get_guidPrefix().toMutableList() as ArrayList<Byte>
        entityId.entityKey = get_entityKey().toMutableList() as ArrayList<Byte>
        entityId.entityKind = get_entityKind()
        guid.entityId = entityId

        sampleIdentity.writer_guid = guid
        header.requestId = sampleIdentity
        val request = BCM_Service_eSrv_Request()
        request.header = header
        val call = BCM_Service_eSrv_Call.descriptor_258667097().apply{
            BCM_VoiceInteractionLight_Ctrl_EnhIn.onoffcmd = onoffcmd as Seres.BCM_eSrv.HPCC_OnOffCmd
            BCM_VoiceInteractionLight_Ctrl_EnhIn.voicelightmode = voicelightmode as Seres.BCM_eSrv.HPCC_VoiceLightMode
        }

        request.data = call
        this.set_sequenceNumber(get_sequenceNumber()+ 1u)
        send_request(request)
        val reply = receive_reply() as BCM_Service_eSrv_Reply
        if(request.header.requestId.sequence_number.low == reply.header.relatedRequestId.sequence_number.low
            && request.header.requestId.sequence_number.high == reply.header.relatedRequestId.sequence_number.high
            && request.header.requestId.writer_guid.guidPrefix.equals(reply.header.relatedRequestId.writer_guid.guidPrefix)
            && request.header.requestId.writer_guid.entityId.entityKey.equals(reply.header.relatedRequestId.writer_guid.entityId.entityKey)
            && request.header.requestId.writer_guid.entityId.entityKind == reply.header.relatedRequestId.writer_guid.entityId.entityKind
        ){

            if(reply.data._d == 258667097) {
                  var return_val = reply.data as BCM_Service_eSrv_Return.descriptor_258667097
                return return_val.BCM_VoiceInteractionLight_Ctrl_EnhResult._return
            } else {
                  throw Exception("[BCM_Service_eSrvClient]--> call BCM_VoiceInteractionLight_Ctrl_Enh unkown _d() value")
              }
        }else{
            throw Exception("[RPCTestClient]--> call myMethod1 requestId not match")
        }
        return ret
    }
}
