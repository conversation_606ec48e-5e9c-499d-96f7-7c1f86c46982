package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_Seat_HeatLevel_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.BCM_eSrv.BCM_CallerID = 0u
    private var _seatid : Seres.BCM_eSrv.HPCC_SeatID = 0u
    private var _heatinglevel : Int = Seres.BCM_eSrv.HeatingLevel.HeatingLevel_No_Request.basevalue

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_Seat_HeatLevel_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_seatid", false),
            Member("_heatinglevel", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var seatid: Seres.BCM_eSrv.HPCC_SeatID
        get() = _seatid
        set(value){
            _seatid = value
        }

    var heatinglevel: Seres.BCM_eSrv.HeatingLevel
        get() = Seres.BCM_eSrv.HeatingLevel.values().first { it.basevalue == _heatinglevel }
        set(value){
            _heatinglevel = value.basevalue
        }

    fun copy(value: BCM_Service_eSrv_BCM_Seat_HeatLevel_Enh_In = this): BCM_Service_eSrv_BCM_Seat_HeatLevel_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._seatid =  value._seatid
            this._heatinglevel =  value._heatinglevel
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, seatid=$seatid, heatinglevel=$heatinglevel)"
    }
}

