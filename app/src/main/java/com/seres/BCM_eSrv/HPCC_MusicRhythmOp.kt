package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class HPCC_MusicRhythmOp : TypeStruct() {
    private var _ambctrl : Seres.BCM_eSrv.HPCC_MusicAmbCtrl = Seres.BCM_eSrv.HPCC_MusicAmbCtrl()
    private var _ambset : Seres.BCM_eSrv.HPCC_AmbRgbBrightnessCtrl = Seres.BCM_eSrv.HPCC_AmbRgbBrightnessCtrl()
    private var _schroederset : Seres.BCM_eSrv.HPCC_SchroederLightCtrl = Seres.BCM_eSrv.HPCC_SchroederLightCtrl()

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::HPCC_MusicRhythmOp"
        orderedMembers = arrayListOf(
            Member("_ambctrl", false),
            Member("_ambset", false),
            Member("_schroederset", false),
        )

        initproperty()
    }

    var ambctrl: Seres.BCM_eSrv.HPCC_MusicAmbCtrl
        get() = _ambctrl
        set(value){
            _ambctrl = value
        }

    var ambset: Seres.BCM_eSrv.HPCC_AmbRgbBrightnessCtrl
        get() = _ambset
        set(value){
            _ambset = value
        }

    var schroederset: Seres.BCM_eSrv.HPCC_SchroederLightCtrl
        get() = _schroederset
        set(value){
            _schroederset = value
        }

    fun copy(value: HPCC_MusicRhythmOp = this): HPCC_MusicRhythmOp{
            this._ambctrl =  value._ambctrl
            this._ambset =  value._ambset
            this._schroederset =  value._schroederset
            return this
        }

    override fun toString(): String{
        return "$typename(ambctrl=$ambctrl, ambset=$ambset, schroederset=$schroederset)"
    }
}

