package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_Reply : TypeStruct() {
    private var _header : dds.rpc.ReplyHeader = dds.rpc.ReplyHeader()
    private var _data : Seres.BCM_eSrv.BCM_Service_eSrv_Return = Seres.BCM_eSrv.BCM_Service_eSrv_Return()

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_Reply"
        orderedMembers = arrayListOf(
            Member("_header", false),
            Member("_data", false, kclass = _data.ukclass),
        )

        initproperty()
    }

    var header: dds.rpc.ReplyHeader
        get() = _header
        set(value){
            _header = value
        }

    var data: Seres.BCM_eSrv.BCM_Service_eSrv_Return
        get() = _data
        set(value){
            _data = value
            updateporperty("_data", _data.ukclass)
        }

    fun copy(value: BCM_Service_eSrv_Reply = this): BCM_Service_eSrv_Reply{
            this._header =  value._header
            this._data =  value._data
            return this
        }

    override fun toString(): String{
        return "$typename(header=$header, data=$data)"
    }
}

