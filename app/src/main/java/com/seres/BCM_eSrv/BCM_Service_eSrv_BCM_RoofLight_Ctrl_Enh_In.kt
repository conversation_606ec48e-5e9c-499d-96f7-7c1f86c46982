package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_RoofLight_Ctrl_Enh_In : TypeStruct() {
    private var _onoffcmd : Seres.BCM_eSrv.HPCC_OnOffCmd = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_RoofLight_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_onoffcmd", false),
        )

        initproperty()
    }

    var onoffcmd: Seres.BCM_eSrv.HPCC_OnOffCmd
        get() = _onoffcmd
        set(value){
            _onoffcmd = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_RoofLight_Ctrl_Enh_In = this): BCM_Service_eSrv_BCM_RoofLight_Ctrl_Enh_In{
            this._onoffcmd =  value._onoffcmd
            return this
        }

    override fun toString(): String{
        return "$typename(onoffcmd=$onoffcmd)"
    }
}

