package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_AmbientLight_Brightness_Ctrl_Enh_In : TypeStruct() {
    private var _ambzoneid : Seres.BCM_eSrv.HCPP_AmbZoneID = 0u
    private var _brightness : Seres.BCM_eSrv.HPCC_Brightness = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_AmbientLight_Brightness_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_ambzoneid", false),
            Member("_brightness", false),
        )

        initproperty()
    }

    var ambzoneid: Seres.BCM_eSrv.HCPP_AmbZoneID
        get() = _ambzoneid
        set(value){
            _ambzoneid = value
        }

    var brightness: Seres.BCM_eSrv.HPCC_Brightness
        get() = _brightness
        set(value){
            _brightness = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_AmbientLight_Brightness_Ctrl_Enh_In = this): BCM_Service_eSrv_BCM_AmbientLight_Brightness_Ctrl_Enh_In{
            this._ambzoneid =  value._ambzoneid
            this._brightness =  value._brightness
            return this
        }

    override fun toString(): String{
        return "$typename(ambzoneid=$ambzoneid, brightness=$brightness)"
    }
}

