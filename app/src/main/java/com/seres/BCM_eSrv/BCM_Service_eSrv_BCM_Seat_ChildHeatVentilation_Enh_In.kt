package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_Seat_ChildHeatVentilation_Enh_In : TypeStruct() {
    private var _seatid : Seres.BCM_eSrv.HPCC_SeatID = 0u
    private var _heatonoff : Seres.BCM_eSrv.HPCC_OnOffCmd = 0u
    private var _ventilationmode : Seres.BCM_eSrv.HPCC_VentilationMode = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_Seat_ChildHeatVentilation_Enh_In"
        orderedMembers = arrayListOf(
            Member("_seatid", false),
            Member("_heatonoff", false),
            Member("_ventilationmode", false),
        )

        initproperty()
    }

    var seatid: Seres.BCM_eSrv.HPCC_SeatID
        get() = _seatid
        set(value){
            _seatid = value
        }

    var heatonoff: Seres.BCM_eSrv.HPCC_OnOffCmd
        get() = _heatonoff
        set(value){
            _heatonoff = value
        }

    var ventilationmode: Seres.BCM_eSrv.HPCC_VentilationMode
        get() = _ventilationmode
        set(value){
            _ventilationmode = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_Seat_ChildHeatVentilation_Enh_In = this): BCM_Service_eSrv_BCM_Seat_ChildHeatVentilation_Enh_In{
            this._seatid =  value._seatid
            this._heatonoff =  value._heatonoff
            this._ventilationmode =  value._ventilationmode
            return this
        }

    override fun toString(): String{
        return "$typename(seatid=$seatid, heatonoff=$heatonoff, ventilationmode=$ventilationmode)"
    }
}

