package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_AmbientLight_RGB_Ctrl_Enh_In : TypeStruct() {
    private var _ambzoneid : Seres.BCM_eSrv.HCPP_AmbZoneID = 0u
    private var _rgb : Seres.BCM_eSrv.HPCC_RGB = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_AmbientLight_RGB_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_ambzoneid", false),
            Member("_rgb", false),
        )

        initproperty()
    }

    var ambzoneid: Seres.BCM_eSrv.HCPP_AmbZoneID
        get() = _ambzoneid
        set(value){
            _ambzoneid = value
        }

    var rgb: Seres.BCM_eSrv.HPCC_RGB
        get() = _rgb
        set(value){
            _rgb = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_AmbientLight_RGB_Ctrl_Enh_In = this): BCM_Service_eSrv_BCM_AmbientLight_RGB_Ctrl_Enh_In{
            this._ambzoneid =  value._ambzoneid
            this._rgb =  value._rgb
            return this
        }

    override fun toString(): String{
        return "$typename(ambzoneid=$ambzoneid, rgb=$rgb)"
    }
}

