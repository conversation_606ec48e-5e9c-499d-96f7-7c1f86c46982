package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_SunRoof_Ctrl_Enh_In : TypeStruct() {
    private var _sunroofid : Seres.BCM_eSrv.HPCC_SunRoofID = 0u
    private var _sunroofop : Seres.BCM_eSrv.HPCC_SunRoofOp = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_SunRoof_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_sunroofid", false),
            Member("_sunroofop", false),
        )

        initproperty()
    }

    var sunroofid: Seres.BCM_eSrv.HPCC_SunRoofID
        get() = _sunroofid
        set(value){
            _sunroofid = value
        }

    var sunroofop: Seres.BCM_eSrv.HPCC_SunRoofOp
        get() = _sunroofop
        set(value){
            _sunroofop = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_SunRoof_Ctrl_Enh_In = this): BCM_Service_eSrv_BCM_SunRoof_Ctrl_Enh_In{
            this._sunroofid =  value._sunroofid
            this._sunroofop =  value._sunroofop
            return this
        }

    override fun toString(): String{
        return "$typename(sunroofid=$sunroofid, sunroofop=$sunroofop)"
    }
}

