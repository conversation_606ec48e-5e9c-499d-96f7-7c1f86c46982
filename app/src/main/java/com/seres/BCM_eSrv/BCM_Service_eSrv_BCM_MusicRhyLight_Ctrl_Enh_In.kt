package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Service_eSrv_BCM_MusicRhyLight_Ctrl_Enh_In : TypeStruct() {
    private var _musicrhythmop : Seres.BCM_eSrv.HPCC_MusicRhythmOp = Seres.BCM_eSrv.HPCC_MusicRhythmOp()

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::BCM_Service_eSrv_BCM_MusicRhyLight_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_musicrhythmop", false),
        )

        initproperty()
    }

    var musicrhythmop: Seres.BCM_eSrv.HPCC_MusicRhythmOp
        get() = _musicrhythmop
        set(value){
            _musicrhythmop = value
        }

    fun copy(value: BCM_Service_eSrv_BCM_MusicRhyLight_Ctrl_Enh_In = this): BCM_Service_eSrv_BCM_MusicRhyLight_Ctrl_Enh_In{
            this._musicrhythmop =  value._musicrhythmop
            return this
        }

    override fun toString(): String{
        return "$typename(musicrhythmop=$musicrhythmop)"
    }
}

