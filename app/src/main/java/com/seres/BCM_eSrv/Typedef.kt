package Seres.BCM_eSrv

typealias HPCC_VoiceLightMode = UInt
typealias HPCC_RGB = UInt
typealias HPCC_Brightness = UByte
typealias HCPP_AmbZoneID = UByte
typealias HPCC_OnOffCmd = UByte
typealias HPCC_AmbientLightTheme = UByte
typealias HPCC_SunRoofOp = UByte
typealias HPCC_SunRoofID = UByte
typealias HPCC_VentilationMode = UByte
typealias HPCC_FoldPara = UByte
typealias HPCC_ZeroGravityPara = UByte
typealias HPCC_SeatID = UByte
typealias Percent = UByte
typealias HPCC_RecircleMode = UByte
typealias HPCC_F_Or_R = UByte
typealias HPCC_MaxHeatStatus = UByte
typealias HPCC_MaxACStatus = UByte
typealias HPCC_ClimateMode_Op = UByte
typealias ACBlwLevel = UByte
typealias HPCC_Temperature = Float
typealias HPCC_ClimateID = UByte
typealias HPCC_OpenSpeed = UByte
typealias HPCC_DoorID = UByte
typealias HPCC_LockUnLockPara = UByte
typealias HPCC_MaxTargetPosition = UByte
typealias ReturnCode = UByte
typealias HPCC_TailGate_Op = UByte
typealias BCM_CallerID = UShort
