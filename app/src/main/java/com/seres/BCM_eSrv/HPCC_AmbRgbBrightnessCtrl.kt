package Seres.BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class HPCC_AmbRgbBrightnessCtrl : TypeStruct() {
    private var _ambzoneid : Seres.BCM_eSrv.HCPP_AmbZoneID = 0u
    private var _brightness : Seres.BCM_eSrv.HPCC_Brightness = 0u
    private var _rgb : Seres.BCM_eSrv.HPCC_RGB = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::BCM_eSrv::HPCC_AmbRgbBrightnessCtrl"
        orderedMembers = arrayListOf(
            Member("_ambzoneid", false),
            Member("_brightness", false),
            Member("_rgb", false),
        )

        initproperty()
    }

    var ambzoneid: Seres.BCM_eSrv.HCPP_AmbZoneID
        get() = _ambzoneid
        set(value){
            _ambzoneid = value
        }

    var brightness: Seres.BCM_eSrv.HPCC_Brightness
        get() = _brightness
        set(value){
            _brightness = value
        }

    var rgb: Seres.BCM_eSrv.HPCC_RGB
        get() = _rgb
        set(value){
            _rgb = value
        }

    fun copy(value: HPCC_AmbRgbBrightnessCtrl = this): HPCC_AmbRgbBrightnessCtrl{
            this._ambzoneid =  value._ambzoneid
            this._brightness =  value._brightness
            this._rgb =  value._rgb
            return this
        }

    override fun toString(): String{
        return "$typename(ambzoneid=$ambzoneid, brightness=$brightness, rgb=$rgb)"
    }
}

