package Seres.HPCC_ZCU_FR_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class RepairMode_setRepairMode_In : TypeStruct() {
    private var _repairmodestatus : Int = Seres.HPCC_ZCU_FR_eSrv.RepairModeStatus.RepairModeStatus_OFF.basevalue

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCC_ZCU_FR_eSrv::RepairMode_setRepairMode_In"
        orderedMembers = arrayListOf(
            Member("_repairmodestatus", false),
        )

        initproperty()
    }

    var repairmodestatus: Seres.HPCC_ZCU_FR_eSrv.RepairModeStatus
        get() = Seres.HPCC_ZCU_FR_eSrv.RepairModeStatus.values().first { it.basevalue == _repairmodestatus }
        set(value){
            _repairmodestatus = value.basevalue
        }

    fun copy(value: RepairMode_setRepairMode_In = this): RepairMode_setRepairMode_In{
            this._repairmodestatus =  value._repairmodestatus
            return this
        }

    override fun toString(): String{
        return "$typename(repairmodestatus=$repairmodestatus)"
    }
}

