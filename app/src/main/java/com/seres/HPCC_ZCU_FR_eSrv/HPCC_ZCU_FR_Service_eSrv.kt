package Seres.HPCC_ZCU_FR_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


open class HPCC_ZCU_FR_Service_eSrv : TypeUnion(){

    protected var __d: Int = 0
    protected var __u: Any? = null

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCC_ZCU_FR_eSrv::HPCC_ZCU_FR_Service_eSrv"
        dmutableMap.put(349695082, descriptor_349695082::class)
    }

    var _d : Int
        get() = __d
        set(value) {
            __d = value
        }

    class descriptor_349695082() : HPCC_ZCU_FR_Service_eSrv(){
        private var _RepairMode_setRepairMode: Seres.HPCC_ZCU_FR_eSrv.RepairMode_setRepairMode_In = Seres.HPCC_ZCU_FR_eSrv.RepairMode_setRepairMode_In()

        init{
            _d = 349695082
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_RepairMode_setRepairMode", false))

            __u = this

            initproperty()
        }

        var RepairMode_setRepairMode: Seres.HPCC_ZCU_FR_eSrv.RepairMode_setRepairMode_In
            get() = _RepairMode_setRepairMode
            set(value) {
                _RepairMode_setRepairMode = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, RepairMode_setRepairMode=$RepairMode_setRepairMode)"
        }

    }

    var _u_descriptor_349695082: descriptor_349695082
    get(){
        __u?.let{
            if ((__u as descriptor_349695082)._d == 349695082){
                return __u!! as descriptor_349695082
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }


}

