package Seres.HPCC_ZCU_FR_eSrv

enum class RepairModeStatus(var basevalue : Int){
    RepairModeStatus_OFF(0),
    RepairModeStatus_ON(1),
    RepairModeStatus_INVALID(255);


    companion object {
        private val valueMap = RepairModeStatus.entries.associateBy { it.basevalue }
        fun fromValue(basevalue: Int): RepairModeStatus{
            return  valueMap[basevalue]?:RepairModeStatus_OFF
        }    
    }
}
