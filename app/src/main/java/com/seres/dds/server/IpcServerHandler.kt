package com.seres.dds.server

import android.os.Bundle
import com.seres.dds.server.consts.InvokeConsts
import com.seres.dds.server.consts.ReturnCode
import com.seres.dds.server.consts.ServiceHash
import com.seres.dds.utils.LogUtils
import seres.s2s.internal.IAsyncResultCallback
import com.seres.dds.commsvc.rpc.ClientManager.checkClientStatus
import com.seres.dds.commsvc.rpc.ClientManager.getClientBCM
import com.seres.dds.commsvc.pubsub.PubManager.checkPubStatus
import com.seres.dds.commsvc.pubsub.PubManager.getPubWriterHpccZcuFr
import com.seres.dds.commsvc.pubsub.PubManager.getPubWriterHpcmBcm
import Seres.HPCM_BCM_eSrv.HPCM_BCM_Service_eSrv
import Seres.HPCC_ZCU_FR_eSrv.HPCC_ZCU_FR_Service_eSrv
import com.seres.dds.server.api.BCM_CHBCmd
import com.seres.dds.server.api.HPCC_MusicRhythmOp
import com.seres.dds.server.api.HPCC_SeatPosition
import com.seres.dds.server.api.ISDEffectCtrl
import com.seres.dds.server.api.ImobAuthStsInfo
import com.seres.dds.server.api.LampEffectCtrl
import com.seres.dds.server.api.SysMode_ReturnCode
import com.seres.dds.server.api.TurnLampindcrReq
import com.seres.dds.server.api.UserInfo
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_TMS_Ventilation_setLevel_ZCUR
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_TMS_Ventilation_setMode_ZCUR
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_TMS_Ventilation_setTemp_ZCUR
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_ChargePortDoor_chargePortOperStr
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_TMS_Demister_Re
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Sunshade_R_setSunshadeEnable
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Sunshade_R_setSunshadeOperationSingle
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Sunshade_R_setSunshadeOperationRAC
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_LvPowerPort_trunk
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RL_MainXDir
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RL_BackRestAngle
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RL_HeatingLevel
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RL_VentilationLevel
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RL_MassagMode
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RL_MassageStrength
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RR_RRFoldFunSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RR_MainXDir
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RR_BackRestAngle
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RR_legRestAngle
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RR_LegRestXDir
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RR_HeatingLevel
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RR_VentilationLevel
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RR_MassagMode
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_RR_MassageStrength
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_ThirdL_BackRestAngle
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_ThirdL_HeatingLevel
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_ThirdL_ThirdLFoldFunSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_ThirdR_BackRestAngle
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_ThirdR_HeatingLevel
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Seat_ThirdR_ThirdRFoldFunSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Tailgate_OpenClose_Ctrl
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_Tailgateopenrat_Ctrl
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_setTailgateopenrat
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_R_BCM_Service_BCM_FuelPortLock_Ctrl
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_TMS_Ventilation_setLevel_ZCUFR
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_TMS_Ventilation_setMode_ZCUFR
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_TMS_Ventilation_setCyclingMode_ZCUFR
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_TMS_Ventilation_setDouble_layerCycling_ZCUFR
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_TMS_Ventilation_setTemp_ZCUFR
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_TMS_Ventilation_setDefostPriority_ZCUFR
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_TMS_EnvMonitor_setIONswitch
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_TMS_EnvMonitor_setUVCswitch
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_TMS_Cfm_setSpeed_ZCUFR
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_RearView_R_RmirrorUDStepSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_RearView_R_RmirrorLRStepSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_RearView_R_RmirrorUDSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_RearView_R_RmirrorLRSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_RearView_R_FoldUnfold
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_RearView_R__RearDefrostSw
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_CoolgHeatgBox_setCHBCmd
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_LvPowerPort_Armrest
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_ElecSideStep_R
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_ElecSideStep_R_SetmotorLearnCMD
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_Seat_FR_MainXDir
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_Seat_FR_BackRestAngle
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_Seat_FR_legRestAngle
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_Seat_FR_LegRestXDir
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_Seat_FR_HeatingLevel
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_Seat_FR_VentilationLevel
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_Seat_FR_MassagMode
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_Seat_FR_MassageStrength
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_Seat_FR_PassSeatFoldFunSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FR_BCM_Service_BCM_Seat_FR_PassSeatLinkageSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_SteerWheel_StartAdjustTilt
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_SteerWheel_StopAdjustTilt
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_SteerWheel_StartAdjustHeight
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_SteerWheel_StopAdjustHeight
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_SteerWheel_SetXmotorLearnCMD
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_SteerWheel_SetZmotorLearnCMD
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_SteerWheel_AdjustTilt
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_SteerWheel_AdjustHeight
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_SteerWhl_Heatr_setHeatCmd
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Horn_stop
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Horn_start
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Horn_alert
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Horn_isRunning
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_RearView_L_LmirrorUDStepSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_RearView_L_LmirrorLRStepSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_RearView_L_LmirrorUDSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_RearView_L_LmirrorLRSet
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_RearView_L_FoldUnfold
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_RearView_L_RearDefrostSw
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Sunshade_F_setSunshadeEnable
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Sunshade_F_setSunshadeOperationSingle
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_RearView_L_powersupply
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_RearView_L_antiGlareenable
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_ElecSideStep_F
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_ElecSideStep_F_SetmotorLearnCMD
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Seat_FL_MainXDir
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Seat_FL_BackRestAngle
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Seat_FL_MainZDir
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Seat_FL_frontZDir
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Seat_FL_HeatingLevel
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Seat_FL_VentilationLevel
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Seat_FL_MassagMode
import com.seres.dds.server.consts.ServiceHash.Hash_ZCU_FL_BCM_Service_BCM_Seat_FL_MassageStrength
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_TailGate_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_FrntHatch_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_ChrgPort_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_FuelFiller_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_SideDoor_ManuaMode_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_SideDoor_MaxTargetPos_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_SideDoor_OpenSpeed_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_BrakePadCloseDoorMode_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Auto_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Temperature_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Temperature_Sync_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Level_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Mode_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_MaxAsHeat_Status_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_ACSwitch_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_Demist_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_Auto_Demist_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_AirPurify_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_TMS_AC_SetBox_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_Auto_Ventilation_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_EMS_RecircleMode_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_EMS_SmartZones_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_EMS_LowVoltage_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_EMS_LowVoltage_Energy_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_EMS_12V_PowerPort_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_SeatCtrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_ZeroGravity_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_Fold_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_HeatLevel_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_VentilationLevel_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_MassageModeCtrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_MassageStrengthCtrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_ChildHeatVentilation_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_ChildLeftBehind_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_SunRoof_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_SunRoof_AUTOLock_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_RoofLight_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_AmbientLight_Theme_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_MusicRhyLight_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_AmbientLight_Brightness_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_AmbientLight_RGB_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_AmbientLight_Sync_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_WelLight_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_NapMode_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_DOW_BSD_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_BCM_Service_eSrv_BCM_VoiceInteractionLight_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_VMM_UsageMode_EnterUsageMode
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_VMM_UsageMode_SetExitDelayTi
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_VMM_CarMode_EnterCarMode
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_VMM_PowerMode_setPowerMode
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Light_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_ADBLight_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_PosnLampEnhance_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Light_BeamPosition_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Light_WidthGuidance_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Light_LowBeamEnhance_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Light_AFS_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Fog_Light_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Extrlight_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_PosnLampDispPattern_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_PosnLampStateDispPattern_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_BatteryDispPattern_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_ThanksDisp_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_GraleLampDisp_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_ADASLampDisp_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Window_ChildLock_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Door_ChildLock_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_CentralLock_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_DrvLock_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_AwayLock_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_approachunlock_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_SeatCS_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_SideDoor_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Window_LockUp_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Window_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Wiper_Sensitivity_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Wiper_Repair_Ctrl_Enh
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_IVI_UserInfoNotify_NTF_UserInfo
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_VIUCenLockStatus
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_LoBeamCmd_FL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_TurnLampCmd_FL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_TurnLampOccupySts_FL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_PosnLampCmd_FL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_HiBeamCmd_FL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_AdpvHiBeamFctOnCmd_FL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_AFSEnaCmd_FL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_PosnLampEffectCtrl_FL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_LowLampEffectCtrl_FL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_VehAntithft_ZCUFL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_EMS_PwrImobAuth_ZCUFL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_BLE_ImobAuth_ZCUFL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Door_RL_ChildLockUnlock_Ctrl
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Door_CenLockStatus
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Door_FL_LockUnlock_Ctrl
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Door_RL_LockUnlock_Ctrl
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_VMM_PowerMode_setPwrSplyOutput_ZCUFL
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_DoorOpenCtrl
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Wiper_F_setBCM_Wpr_Rq
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Wiper_F_setBCM_Wpr_Splash
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Wiper_F_setBCM_Wpr_Stop_ImmRq
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Wiper_F_setBCM_Wpr_Posn_Rq
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Wiper_F_setBCM_Block_OverWrite
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Wiper_F_setBCM_Wpr_EN
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Wiper_F_setWipingPlantMode
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Wiper_CS
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Window_FL_adjustPosition
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Window_FL_open
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Window_FL_close
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Window_FL_stop
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Window_RL_adjustPosition
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Window_RL_open
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Window_RL_close
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FL_Control_BCM_Window_RL_stop
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_IVI_UserInfoNotify_NTF_UserInfo
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_VEH_SysMode_sysModServReq
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_LoBeamCmd_FR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_TurnLampCmd_FR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_TurnLampOccupySts_FR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_PosnLampCmd_FR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_HiBeamCmd_FR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_AdpvHiBeamFctOnCmd_FR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_AFSEnaCmd_FR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_PosnLampindcrReq
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_LoBeamindcrReq
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_HiBeamindcrReq
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_TurnLampindcrReq
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_AFSFltIndcrReq
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_TurnLampBuzReq
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_ReFogLampindcrReq
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_PosnLampEffectCtrl_FR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_LowLampEffectCtrl_FR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_VehAntithft_ZCUFR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_EMS_PwrImobAuth_ZCUFR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_BLE_ImobAuth_ZCUFR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_Door_RR_ChildLockUnlock_Ctrl
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_Door_FR_LockUnlock_Ctrl
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_Door_RR_LockUnlock_Ctrl
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_VMM_PowerMode_setPwrSplyOutput_ZCUFR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_DoorOpenCtrl
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_WiperWash_stopSprayWashing
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_WiperWash_R_startSprayWashing
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_WiperWash_F_startSprayWashing
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_Window_FR_adjustPosition
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_Window_FR_open
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_Window_FR_close
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_Window_FR_stop
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_Window_RR_adjustPosition
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_Window_RR_open
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_Window_RR_close
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_FR_Control_BCM_Window_RR_stop
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_IVI_UserInfoNotify_NTF_UserInfo
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_BrkLampCmd
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_ReFogLampCmd
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_RvsLampCmd
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_TurnLampCmd_R
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_TurnLampOccupySts_R
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_PosnLampCmd_R
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_PosnLampEffectCtrl_R
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_ISDEffectCtrl_R
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_PosnLamStatusSet_R
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_LicensePlateLightSet_R
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_VehAntithft_ZCUR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_EMS_PwrImobAuth_ZCUR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_BLE_ImobAuth_ZCUR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_VMM_PowerMode_setPwrSplyOutput_ZCUR
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_WiperWash_R_stopWiping
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_WiperWash_R_startWiping
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_WiperWash_R_IntermittentWiping
import com.seres.dds.server.consts.ServiceHash.Hash_HPCM_R_Control_BCM_WiperWash_R_MaintenanceMode
import com.seres.dds.server.consts.ServiceHash.Hash_HPCT_INI_XCALL_XCALLOperation
import com.seres.dds.server.consts.ServiceHash.Hash_HPCT_INI_XCALL_XCALLStatusReport
import com.seres.dds.server.consts.ServiceHash.Hash_HPCC_ZCU_FR_Service_eSrv_RepairMode_setRepairMode
import Seres.BCM_eSrv.ACZoneId
import Seres.BCM_eSrv.HeatingLevel
import Seres.BCM_eSrv.VentilationLevel
import Seres.BCM_eSrv.MassagMode
import Seres.BCM_eSrv.MassageStrength
import Seres.HPCM_BCM_eSrv.UsageMode
import Seres.HPCM_BCM_eSrv.PowerMode
import Seres.HPCM_BCM_eSrv.CS_WiperSensitivityLevel
import Seres.HPCC_ZCU_FR_eSrv.RepairModeStatus



/**
 * 处理客户端调用s2s服务的实现类
 *
 * @constructor Create empty Server impl
 */
object IpcServerHandler {
    private const val TAG = "ServerImpl"

    /**
     * 同步处理客户端对s2s服务的请求
     *
     * @param appId
     * @param serviceHashId 要调用的服务hashId值
     * @param params
     */
    fun handleRequestSync(appId: Int?, serviceHashId: Int, params: Bundle?): Bundle {
        return handleRequest(serviceHashId, params)
    }

    /**
     * 异步处理客户端对s2s服务的请求
     *
     * @param appId
     * @param serviceHashId 要调用的服务hashId值
     * @param params
     * @param callback
     */
    fun handleRequestAsync(appId: Int, serviceHashId: Int, params: Bundle?, callback: IAsyncResultCallback?) {
        val resultBundle = handleRequest(serviceHashId, params)
        callback?.onResult(resultBundle)
    }

    /**
     * 处理client端服务调用
     *
     * @param serviceHashId 要调用的服务hashId值
     * @param params 入参
     * @return 返回bundle，返回值在bundle中
     */
    private fun handleRequest(serviceHashId: Int, params: Bundle?): Bundle {
        val bundle = Bundle()
        var ret = 0.toUByte()
        // TODO 具体实现处理客户端方法调用
        when (serviceHashId) {

            ServiceHash.Hash_BCM_Service_eSrv_BCM_TailGate_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val tailgate_op = params?.getInt("tailgate_op")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, tailgate_op = $tailgate_op")

                if( bcm_callerid != null && tailgate_op != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_TailGate_Ctrl_Enh(bcm_callerid.toUShort(), tailgate_op.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val maxtargetposition = params?.getInt("maxtargetposition")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, maxtargetposition = $maxtargetposition")

                if( bcm_callerid != null && maxtargetposition != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh(bcm_callerid.toUShort(), maxtargetposition.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_FrntHatch_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val frnhatch_op = params?.getInt("frnhatch_op")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, frnhatch_op = $frnhatch_op")

                if( bcm_callerid != null && frnhatch_op != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_FrntHatch_Ctrl_Enh(bcm_callerid.toUShort(), frnhatch_op.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_ChrgPort_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_ChrgPort_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_FuelFiller_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_FuelFiller_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_SideDoor_ManuaMode_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_SideDoor_ManuaMode_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_SideDoor_MaxTargetPos_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val doorid = params?.getInt("doorid")
                val maxtargetposition = params?.getInt("maxtargetposition")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, doorid = $doorid, maxtargetposition = $maxtargetposition")

                if( bcm_callerid != null && doorid != null && maxtargetposition != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_SideDoor_MaxTargetPos_Ctrl_Enh(bcm_callerid.toUShort(), doorid.toUByte(), maxtargetposition.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_SideDoor_OpenSpeed_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val openspeed = params?.getInt("openspeed")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, openspeed = $openspeed")

                if( bcm_callerid != null && openspeed != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_SideDoor_OpenSpeed_Ctrl_Enh(bcm_callerid.toUShort(), openspeed.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_BrakePadCloseDoorMode_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_BrakePadCloseDoorMode_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val climateid = params?.getInt("climateid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, climateid = $climateid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && climateid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_Climate_Ctrl_Enh(bcm_callerid.toUShort(), climateid.toUByte(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Auto_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val climateid = params?.getInt("climateid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, climateid = $climateid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && climateid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_Climate_Auto_Ctrl_Enh(bcm_callerid.toUShort(), climateid.toUByte(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Temperature_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val zoneid = params?.getInt("zoneid")
                val temperature = params?.getFloat("temperature")
                val cmd : ACZoneId = when(zoneid){
                    0x0 -> ACZoneId.ACZONE_FRONT_LEFT
                    0x1 -> ACZoneId.ACZONE_FRONT_RIGH
                    0x2 -> ACZoneId.ACZONE_REAR_LEFT
                    0x3 -> ACZoneId.ACZONE_REAR_RIGHT
                    0x4 -> ACZoneId.ACZONE_THIRD_LEFT
                    0x5 -> ACZoneId.ACZONE_THIRD_RIGHT
//                    0xFE -> ACZoneId.ACZoneId_intial
                    0xFF -> ACZoneId.ACZoneId_INVALID
                    else -> throw IllegalArgumentException("Invalid value for zoneid: $zoneid")
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, zoneid = $zoneid, temperature = $temperature")

                if( bcm_callerid != null && zoneid != null && temperature != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_Climate_Temperature_Ctrl_Enh(bcm_callerid.toUShort(), cmd, temperature.toFloat())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Temperature_Sync_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_Climate_Temperature_Sync_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Level_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val climateid = params?.getInt("climateid")
                val acblwlevel = params?.getInt("acblwlevel")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, climateid = $climateid, acblwlevel = $acblwlevel")

                if( bcm_callerid != null && climateid != null && acblwlevel != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_Climate_Level_Ctrl_Enh(bcm_callerid.toUShort(), climateid.toUByte(), acblwlevel.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_Climate_Mode_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val climateid = params?.getInt("climateid")
                val climatemode_op = params?.getInt("climatemode_op")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, climateid = $climateid, climatemode_op = $climatemode_op, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && climateid != null && climatemode_op != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_Climate_Mode_Ctrl_Enh(bcm_callerid.toUShort(), climateid.toUByte(), climatemode_op.toUByte(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_MaxAsHeat_Status_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val maxacstatus = params?.getInt("maxacstatus")
                val maxheatstatus = params?.getInt("maxheatstatus")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, maxacstatus = $maxacstatus, maxheatstatus = $maxheatstatus")

                if( bcm_callerid != null && maxacstatus != null && maxheatstatus != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_MaxAsHeat_Status_Ctrl_Enh(bcm_callerid.toUShort(), maxacstatus.toUByte(), maxheatstatus.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_ACSwitch_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_ACSwitch_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_Demist_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val f_or_r = params?.getInt("f_or_r")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, f_or_r = $f_or_r, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && f_or_r != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_Demist_Ctrl_Enh(bcm_callerid.toUShort(), f_or_r.toUByte(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_Auto_Demist_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_Auto_Demist_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_AirPurify_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_AirPurify_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_TMS_AC_SetBox_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().TMS_AC_SetBox_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_Auto_Ventilation_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_Auto_Ventilation_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_EMS_RecircleMode_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val recirclemode = params?.getInt("recirclemode")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, recirclemode = $recirclemode")

                if( bcm_callerid != null && recirclemode != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().EMS_RecircleMode_Ctrl_Enh(bcm_callerid.toUShort(), recirclemode.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_EMS_SmartZones_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().EMS_SmartZones_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_EMS_LowVoltage_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().EMS_LowVoltage_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_EMS_LowVoltage_Energy_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid")

                if( bcm_callerid != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().EMS_LowVoltage_Energy_Ctrl_Enh(bcm_callerid.toUShort())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_EMS_12V_PowerPort_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if( bcm_callerid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().EMS_12V_PowerPort_Ctrl_Enh(bcm_callerid.toUShort(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_SeatCtrl_Enh -> {

                // 设置parcelable 参数解析classLoader
                params?.classLoader = HPCC_SeatPosition::class.java.classLoader
                val seatposition = params?.getParcelable<HPCC_SeatPosition>("seatposition")

                val seatposition_trans:Seres.BCM_eSrv.HPCC_SeatPosition = Seres.BCM_eSrv.HPCC_SeatPosition()

                if (seatposition != null) {
                    val seat_fl = Seres.BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_fl.mainxdir.toUByte()
                        mainzdir = seatposition.seat_fl.mainzdir.toUByte()
                        frontzdir = seatposition.seat_fl.frontzdir.toUByte()
                        backrestangle = seatposition.seat_fl.backrestangle.toUByte()
                        legrestangle = seatposition.seat_fl.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_fl.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_fl.footrestangle.toUByte()
                        frontxdir = seatposition.seat_fl.frontxdir.toUByte()

                    }
                    val seat_fr = Seres.BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_fr.mainxdir.toUByte()
                        mainzdir = seatposition.seat_fr.mainzdir.toUByte()
                        frontzdir = seatposition.seat_fr.frontzdir.toUByte()
                        backrestangle = seatposition.seat_fr.backrestangle.toUByte()
                        legrestangle = seatposition.seat_fr.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_fr.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_fr.footrestangle.toUByte()
                        frontxdir = seatposition.seat_fr.frontxdir.toUByte()

                    }
                    val seat_rl = Seres.BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_rl.mainxdir.toUByte()
                        mainzdir = seatposition.seat_rl.mainzdir.toUByte()
                        frontzdir = seatposition.seat_rl.frontzdir.toUByte()
                        backrestangle = seatposition.seat_rl.backrestangle.toUByte()
                        legrestangle = seatposition.seat_rl.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_rl.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_rl.footrestangle.toUByte()
                        frontxdir = seatposition.seat_rl.frontxdir.toUByte()

                    }
                    val seat_rm = Seres.BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_rm.mainxdir.toUByte()
                        mainzdir = seatposition.seat_rm.mainzdir.toUByte()
                        frontzdir = seatposition.seat_rm.frontzdir.toUByte()
                        backrestangle = seatposition.seat_rm.backrestangle.toUByte()
                        legrestangle = seatposition.seat_rm.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_rm.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_rm.footrestangle.toUByte()
                        frontxdir = seatposition.seat_rm.frontxdir.toUByte()

                    }
                    val seat_rr = Seres.BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_rr.mainxdir.toUByte()
                        mainzdir = seatposition.seat_rr.mainzdir.toUByte()
                        frontzdir = seatposition.seat_rr.frontzdir.toUByte()
                        backrestangle = seatposition.seat_rr.backrestangle.toUByte()
                        legrestangle = seatposition.seat_rr.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_rr.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_rr.footrestangle.toUByte()
                        frontxdir = seatposition.seat_rr.frontxdir.toUByte()

                    }
                    val seat_thirdl = Seres.BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_thirdl.mainxdir.toUByte()
                        mainzdir = seatposition.seat_thirdl.mainzdir.toUByte()
                        frontzdir = seatposition.seat_thirdl.frontzdir.toUByte()
                        backrestangle = seatposition.seat_thirdl.backrestangle.toUByte()
                        legrestangle = seatposition.seat_thirdl.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_thirdl.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_thirdl.footrestangle.toUByte()
                        frontxdir = seatposition.seat_thirdl.frontxdir.toUByte()

                    }
                    val seat_thirdm = Seres.BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_thirdm.mainxdir.toUByte()
                        mainzdir = seatposition.seat_thirdm.mainzdir.toUByte()
                        frontzdir = seatposition.seat_thirdm.frontzdir.toUByte()
                        backrestangle = seatposition.seat_thirdm.backrestangle.toUByte()
                        legrestangle = seatposition.seat_thirdm.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_thirdm.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_thirdm.footrestangle.toUByte()
                        frontxdir = seatposition.seat_thirdm.frontxdir.toUByte()

                    }
                    val seat_thirdr = Seres.BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_thirdr.mainxdir.toUByte()
                        mainzdir = seatposition.seat_thirdr.mainzdir.toUByte()
                        frontzdir = seatposition.seat_thirdr.frontzdir.toUByte()
                        backrestangle = seatposition.seat_thirdr.backrestangle.toUByte()
                        legrestangle = seatposition.seat_thirdr.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_thirdr.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_thirdr.footrestangle.toUByte()
                        frontxdir = seatposition.seat_thirdr.frontxdir.toUByte()

                    }
                    // 设置转换后的值
                    seatposition_trans.seat_fl = seat_fl
                    seatposition_trans.seat_fr = seat_fr
                    seatposition_trans.seat_rl = seat_rl
                    seatposition_trans.seat_rm = seat_rm
                    seatposition_trans.seat_rr = seat_rr
                    seatposition_trans.seat_thirdl = seat_thirdl
                    seatposition_trans.seat_thirdm = seat_thirdm
                    seatposition_trans.seat_thirdr = seat_thirdr
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId seatposition = $seatposition")

                if( seatposition != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_SeatCtrl_Enh(seatposition_trans)
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_ZeroGravity_Ctrl_Enh -> {

                val seatid = params?.getInt("seatid")
                val zerogravityparam = params?.getInt("zerogravityparam")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId seatid = $seatid, zerogravityparam = $zerogravityparam")

                if( seatid != null && zerogravityparam != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_Seat_ZeroGravity_Ctrl_Enh(seatid.toUByte(), zerogravityparam.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh -> {

                val seatid = params?.getInt("seatid")
                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId seatid = $seatid, onoffcmd = $onoffcmd")

                if( seatid != null && onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh(seatid.toUByte(), onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_Fold_Ctrl_Enh -> {

                val seatid = params?.getInt("seatid")
                val foldpara = params?.getInt("foldpara")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId seatid = $seatid, foldpara = $foldpara")

                if( seatid != null && foldpara != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_Seat_Fold_Ctrl_Enh(seatid.toUByte(), foldpara.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_HeatLevel_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val seatid = params?.getInt("seatid")
                val heatingLevel = params?.getInt("heatingLevel")
                val cmd : HeatingLevel = when(heatingLevel){
                    0x0 -> HeatingLevel. HeatingLevel_No_Request
                    0x1 -> HeatingLevel. HeatingLevel_OFF
                    0x2 -> HeatingLevel. HeatingLevel_1
                    0x3 -> HeatingLevel. HeatingLevel_2
                    0x4 -> HeatingLevel. HeatingLevel_3
                    0xFF -> HeatingLevel. HeatingLevel_Invalid
                    else -> throw IllegalArgumentException("Invalid value for heatingLevel: $heatingLevel")
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, seatid = $seatid, heatingLevel = $heatingLevel")

                if( bcm_callerid != null && seatid != null && heatingLevel != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_Seat_HeatLevel_Enh(bcm_callerid.toUShort(), seatid.toUByte(), cmd)
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_VentilationLevel_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val seatid = params?.getInt("seatid")
                val ventilationlevel = params?.getInt("ventilationlevel")
                val cmd : VentilationLevel = when(ventilationlevel){
                    0x0 -> VentilationLevel. VentilationLevel_No_Request
                    0x1 -> VentilationLevel. VentilationLevel_OFF
                    0x2 -> VentilationLevel. VentilationLevel_1
                    0x3 -> VentilationLevel. VentilationLevel_2
                    0x4 -> VentilationLevel. VentilationLevel_3
                    0xFF -> VentilationLevel. VentilationLevel_Invalid
                    else -> throw IllegalArgumentException("Invalid value for ventilationlevel: $ventilationlevel")
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, seatid = $seatid, ventilationlevel = $ventilationlevel")

                if( bcm_callerid != null && seatid != null && ventilationlevel != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_Seat_VentilationLevel_Enh(bcm_callerid.toUShort(), seatid.toUByte(), cmd)
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_MassageModeCtrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val seatid = params?.getInt("seatid")
                val massagMode = params?.getInt("massagMode")
                val cmd : MassagMode = when(massagMode){
                    0x0 -> MassagMode. MassagMode_No_Request
                    0x1 -> MassagMode. MassagMode_OFF
                    0x2 -> MassagMode. MassagMode_1
                    0x3 -> MassagMode. MassagMode_2
                    0x4 -> MassagMode. MassagMode_3
                    0xFF -> MassagMode. MassagMode_INVALID
                    else -> throw IllegalArgumentException("Invalid value for massagMode: $massagMode")
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, seatid = $seatid, massagMode = $massagMode")

                if( bcm_callerid != null && seatid != null && massagMode != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_Seat_MassageModeCtrl_Enh(bcm_callerid.toUShort(), seatid.toUByte(), cmd)
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_MassageStrengthCtrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val seatid = params?.getInt("seatid")
                val massageStrength = params?.getInt("massageStrength")
                val cmd : MassageStrength = when(massageStrength){
                    0x0 -> MassageStrength. MassageStrength_No_Request
                    0x1 -> MassageStrength. MassageStrength_OFF
                    0x2 -> MassageStrength. MassageStrength_SOFT
                    0x3 -> MassageStrength. MassageStrength_MEDIUM
                    0x4 -> MassageStrength. MassageStrength_HEAVY
                    0xFF -> MassageStrength. MassageStrength_INVALID
                    else -> throw IllegalArgumentException("Invalid value for massageStrength: $massageStrength")
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, seatid = $seatid, massageStrength = $massageStrength")

                if( bcm_callerid != null && seatid != null && massageStrength != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_Seat_MassageStrengthCtrl_Enh(bcm_callerid.toUShort(), seatid.toUByte(), cmd)
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_ChildHeatVentilation_Enh -> {

                val seatid = params?.getInt("seatid")
                val heatonoff = params?.getInt("heatonoff")
                val ventilationmode = params?.getInt("ventilationmode")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId seatid = $seatid, heatonoff = $heatonoff, ventilationmode = $ventilationmode")

                if( seatid != null && heatonoff != null && ventilationmode != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_Seat_ChildHeatVentilation_Enh(seatid.toUByte(), heatonoff.toUByte(), ventilationmode.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_Seat_ChildLeftBehind_Enh -> {

                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId onoffcmd = $onoffcmd")

                if( onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_Seat_ChildLeftBehind_Enh(onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_SunRoof_Ctrl_Enh -> {

                val sunroofid = params?.getInt("sunroofid")
                val sunroofop = params?.getInt("sunroofop")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId sunroofid = $sunroofid, sunroofop = $sunroofop")

                if( sunroofid != null && sunroofop != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_SunRoof_Ctrl_Enh(sunroofid.toUByte(), sunroofop.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_SunRoof_AUTOLock_Enh -> {

                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId onoffcmd = $onoffcmd")

                if( onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_SunRoof_AUTOLock_Enh(onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_RoofLight_Ctrl_Enh -> {

                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId onoffcmd = $onoffcmd")

                if( onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_RoofLight_Ctrl_Enh(onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_AmbientLight_Theme_Enh -> {

                val theme = params?.getInt("theme")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId theme = $theme")

                if( theme != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_AmbientLight_Theme_Enh(theme.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_MusicRhyLight_Ctrl_Enh -> {

                // 设置parcelable 参数解析classLoader
                params?.classLoader = HPCC_MusicRhythmOp::class.java.classLoader
                val musicrhythmop = params?.getParcelable<HPCC_MusicRhythmOp>("musicrhythmop")

                val musicrhythmop_trans:Seres.BCM_eSrv.HPCC_MusicRhythmOp = Seres.BCM_eSrv.HPCC_MusicRhythmOp()

                if (musicrhythmop != null) {
                    val ambctrl = Seres.BCM_eSrv.HPCC_MusicAmbCtrl().apply{
                        ambzoneid = musicrhythmop.ambctrl.ambzoneid.toUByte()
                        onoffcmd = musicrhythmop.ambctrl.onoffcmd.toUByte()

                    }
                    val ambset = Seres.BCM_eSrv.HPCC_AmbRgbBrightnessCtrl().apply{
                        ambzoneid = musicrhythmop.ambset.ambzoneid.toUByte()
                        brightness = musicrhythmop.ambset.brightness.toUByte()
                        rgb = musicrhythmop.ambset.rgb.toUInt()

                    }
                    val schroederset = Seres.BCM_eSrv.HPCC_SchroederLightCtrl().apply{
                        brightness = musicrhythmop.schroederset.brightness.toUByte()
                        rgb = musicrhythmop.schroederset.rgb.toUInt()

                    }
                    // 设置转换后的值
                    musicrhythmop_trans.ambctrl = ambctrl
                    musicrhythmop_trans.ambset = ambset
                    musicrhythmop_trans.schroederset = schroederset
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId musicrhythmop = $musicrhythmop")

                if( musicrhythmop != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_MusicRhyLight_Ctrl_Enh(musicrhythmop_trans)
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_AmbientLight_Brightness_Ctrl_Enh -> {

                val ambzoneid = params?.getInt("ambzoneid")
                val brightness = params?.getInt("brightness")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId ambzoneid = $ambzoneid, brightness = $brightness")

                if( ambzoneid != null && brightness != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_AmbientLight_Brightness_Ctrl_Enh(ambzoneid.toUByte(), brightness.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_AmbientLight_RGB_Ctrl_Enh -> {

                val ambzoneid = params?.getInt("ambzoneid")
                val rgb = params?.getInt("rgb")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId ambzoneid = $ambzoneid, rgb = $rgb")

                if( ambzoneid != null && rgb != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_AmbientLight_RGB_Ctrl_Enh(ambzoneid.toUByte(), rgb.toUInt())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_AmbientLight_Sync_Enh -> {

                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId onoffcmd = $onoffcmd")

                if( onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_AmbientLight_Sync_Enh(onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_WelLight_Ctrl_Enh -> {

                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId onoffcmd = $onoffcmd")

                if( onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_WelLight_Ctrl_Enh(onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_NapMode_Ctrl_Enh -> {

                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId onoffcmd = $onoffcmd")

                if( onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_NapMode_Ctrl_Enh(onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_DOW_BSD_Ctrl_Enh -> {

                val onoffcmd = params?.getInt("onoffcmd")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId onoffcmd = $onoffcmd")

                if( onoffcmd != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_DOW_BSD_Ctrl_Enh(onoffcmd.toUByte())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_BCM_Service_eSrv_BCM_VoiceInteractionLight_Ctrl_Enh -> {

                val onoffcmd = params?.getInt("onoffcmd")
                val voicelightmode = params?.getInt("voicelightmode")
                LogUtils.i(TAG, "serviceHashId = $serviceHashId onoffcmd = $onoffcmd, voicelightmode = $voicelightmode")

                if( onoffcmd != null && voicelightmode != null && checkClientStatus("BCM_Service_eSrv")){
                    ret = getClientBCM().BCM_VoiceInteractionLight_Ctrl_Enh(onoffcmd.toUByte(), voicelightmode.toUInt())
                    LogUtils.i(TAG, "BCM_NapMode_Ctrl_Enh ret is : " + ret)
                }
            }

            ServiceHash.Hash_HPCM_BCM_Service_eSrv_VMM_UsageMode_EnterUsageMode -> {

                val AppSrv_CallerIdArg = params?.getInt("AppSrv_CallerIdArg")
                val UsageModeArg = params?.getInt("UsageModeArg")

                val data = HPCM_BCM_Service_eSrv.descriptor_1656882926()
                data.VMM_UsageMode_EnterUsageMode = Seres.HPCM_BCM_eSrv.VMM_UsageMode_EnterUsageMode_In()

                if( AppSrv_CallerIdArg != null ) {
                    data.VMM_UsageMode_EnterUsageMode.appsrv_calleridarg = AppSrv_CallerIdArg.toUShort()
                }
                val cmd : UsageMode = when(UsageModeArg){
                    0x0 -> UsageMode.USAGEMODE_ABANDONED
                    0x1 -> UsageMode.USAGEMODE_INACTIVE
                    0x2 -> UsageMode.USAGEMODE_CONVENIENCE
                    0x3 -> UsageMode.USAGEMODE_DRIVING
                    0x4 -> UsageMode.USAGEMODE_OTAUPDATING
                    0x5 -> UsageMode.USAGEMODE_REMOTE
                    0x6 -> UsageMode.USAGEMODE_REMOTEDRIVING
                    0x7 -> UsageMode.USAGEMODE_RESERVED
                    0xF -> UsageMode.USAGEMODE_INVALID
                    else -> throw IllegalArgumentException("Invalid value for UsageModeArg: $UsageModeArg")
                }
                data.VMM_UsageMode_EnterUsageMode.usagemodearg = cmd
                data._d = Hash_HPCM_BCM_Service_eSrv_VMM_UsageMode_EnterUsageMode
                LogUtils.i(TAG, "serviceHashId = $serviceHashId AppSrv_CallerIdArg = $AppSrv_CallerIdArg, UsageModeArg = $UsageModeArg")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_VMM_UsageMode_EnterUsageMode ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_VMM_UsageMode_SetExitDelayTi -> {

                val AppSrv_CallerIdArg = params?.getInt("AppSrv_CallerIdArg")
                val ExitDelayTiArg = params?.getInt("ExitDelayTiArg")

                val data = HPCM_BCM_Service_eSrv.descriptor__920425368()
                data.VMM_UsageMode_SetExitDelayTi = Seres.HPCM_BCM_eSrv.VMM_UsageMode_SetExitDelayTi_In()

                if( AppSrv_CallerIdArg != null ) {
                    data.VMM_UsageMode_SetExitDelayTi.appsrv_calleridarg = AppSrv_CallerIdArg.toUShort()
                }
                if( ExitDelayTiArg != null ) {
                    data.VMM_UsageMode_SetExitDelayTi.exitdelaytiarg = ExitDelayTiArg.toUShort()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId AppSrv_CallerIdArg = $AppSrv_CallerIdArg, ExitDelayTiArg = $ExitDelayTiArg")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_VMM_UsageMode_SetExitDelayTi ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_VMM_CarMode_EnterCarMode -> {

                val appSrv_CallerId = params?.getInt("appSrv_CallerId")
                val carMode = params?.getInt("carMode")

                val data = HPCM_BCM_Service_eSrv.descriptor_1822396041()
                data.VMM_CarMode_EnterCarMode = Seres.HPCM_BCM_eSrv.VMM_CarMode_EnterCarMode_In()

                if( appSrv_CallerId != null ) {
                    data.VMM_CarMode_EnterCarMode.appsrv_callerid = appSrv_CallerId.toUShort()
                }
                if( carMode != null ) {
                    data.VMM_CarMode_EnterCarMode.carmode = carMode.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId appSrv_CallerId = $appSrv_CallerId, carMode = $carMode")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_VMM_CarMode_EnterCarMode ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_VMM_PowerMode_setPowerMode -> {

                val AppSrv_CallerIdArg = params?.getInt("AppSrv_CallerIdArg")
                val PowerModeArg = params?.getInt("PowerModeArg")

                val data = HPCM_BCM_Service_eSrv.descriptor_1856911191()
                data.VMM_PowerMode_setPowerMode = Seres.HPCM_BCM_eSrv.VMM_PowerMode_setPowerMode_In()

                if( AppSrv_CallerIdArg != null ) {
                    data.VMM_PowerMode_setPowerMode.appsrv_calleridarg = AppSrv_CallerIdArg.toUShort()
                }
                val cmd : PowerMode = when(PowerModeArg){
                    0x0 -> PowerMode.POWERMODE_OFF
                    0x1 -> PowerMode.POWERMODE_ON
                    0x2 -> PowerMode.POWERMODE_RESERVED
                    0x3 -> PowerMode.POWERMODE_INVALID
                    else -> throw IllegalArgumentException("Invalid value for PowerModeArg: $PowerModeArg")
                }
                data.VMM_PowerMode_setPowerMode.powermodearg = cmd
                data._d = Hash_HPCM_BCM_Service_eSrv_VMM_PowerMode_setPowerMode
                LogUtils.i(TAG, "serviceHashId = $serviceHashId AppSrv_CallerIdArg = $AppSrv_CallerIdArg, PowerModeArg = $PowerModeArg")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_VMM_PowerMode_setPowerMode ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Light_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor_66238767()
                data.BCM_Light_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Light_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Light_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_Light_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Light_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_ADBLight_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor_655960075()
                data.BCM_ADBLight_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_ADBLight_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_ADBLight_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_ADBLight_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_ADBLight_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_PosnLampEnhance_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor_505398926()
                data.BCM_PosnLampEnhance_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_PosnLampEnhance_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_PosnLampEnhance_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_PosnLampEnhance_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_PosnLampEnhance_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Light_BeamPosition_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val zangle = params?.getInt("zangle")

                val data = HPCM_BCM_Service_eSrv.descriptor_207009957()
                data.BCM_Light_BeamPosition_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Light_BeamPosition_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Light_BeamPosition_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( zangle != null ) {
                    data.BCM_Light_BeamPosition_Ctrl_Enh.zangle = zangle.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, zangle = $zangle")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Light_BeamPosition_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Light_WidthGuidance_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor__763709823()
                data.BCM_Light_WidthGuidance_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Light_WidthGuidance_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Light_WidthGuidance_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_Light_WidthGuidance_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Light_WidthGuidance_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Light_LowBeamEnhance_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor__1478517379()
                data.BCM_Light_LowBeamEnhance_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Light_LowBeamEnhance_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Light_LowBeamEnhance_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_Light_LowBeamEnhance_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Light_LowBeamEnhance_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Light_AFS_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor__950422755()
                data.BCM_Light_AFS_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Light_AFS_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Light_AFS_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_Light_AFS_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Light_AFS_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Fog_Light_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor__1403693521()
                data.BCM_Fog_Light_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Fog_Light_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Fog_Light_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_Fog_Light_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Fog_Light_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Extrlight_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val cfgPara = params?.getInt("cfgPara")

                val data = HPCM_BCM_Service_eSrv.descriptor_100069557()
                data.BCM_Extrlight_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Extrlight_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Extrlight_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( cfgPara != null ) {
                    data.BCM_Extrlight_Ctrl_Enh.cfgpara = cfgPara.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, cfgPara = $cfgPara")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Extrlight_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_PosnLampDispPattern_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val intelsigdisp_op = params?.getInt("intelsigdisp_op")

                val data = HPCM_BCM_Service_eSrv.descriptor__1750758886()
                data.BCM_PosnLampDispPattern_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_PosnLampDispPattern_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_PosnLampDispPattern_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( intelsigdisp_op != null ) {
                    data.BCM_PosnLampDispPattern_Ctrl_Enh.intelsigdisp_op = intelsigdisp_op.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, intelsigdisp_op = $intelsigdisp_op")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_PosnLampDispPattern_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_PosnLampStateDispPattern_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val intelsigdisp_op = params?.getInt("intelsigdisp_op")

                val data = HPCM_BCM_Service_eSrv.descriptor_1305593815()
                data.BCM_PosnLampStateDispPattern_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_PosnLampStateDispPattern_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_PosnLampStateDispPattern_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( intelsigdisp_op != null ) {
                    data.BCM_PosnLampStateDispPattern_Ctrl_Enh.intelsigdisp_op = intelsigdisp_op.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, intelsigdisp_op = $intelsigdisp_op")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_PosnLampStateDispPattern_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_BatteryDispPattern_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val intelsigdisp_op = params?.getInt("intelsigdisp_op")

                val data = HPCM_BCM_Service_eSrv.descriptor__1694013410()
                data.BCM_BatteryDispPattern_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_BatteryDispPattern_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_BatteryDispPattern_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( intelsigdisp_op != null ) {
                    data.BCM_BatteryDispPattern_Ctrl_Enh.intelsigdisp_op = intelsigdisp_op.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, intelsigdisp_op = $intelsigdisp_op")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_BatteryDispPattern_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_ThanksDisp_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor__1484169858()
                data.BCM_ThanksDisp_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_ThanksDisp_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_ThanksDisp_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_ThanksDisp_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_ThanksDisp_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_GraleLampDisp_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor_1826448851()
                data.BCM_GraleLampDisp_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_GraleLampDisp_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_GraleLampDisp_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_GraleLampDisp_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_GraleLampDisp_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_ADASLampDisp_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val intelsigdisp_op = params?.getInt("intelsigdisp_op")

                val data = HPCM_BCM_Service_eSrv.descriptor__986204298()
                data.BCM_ADASLampDisp_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_ADASLampDisp_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_ADASLampDisp_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( intelsigdisp_op != null ) {
                    data.BCM_ADASLampDisp_Ctrl_Enh.intelsigdisp_op = intelsigdisp_op.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, intelsigdisp_op = $intelsigdisp_op")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_ADASLampDisp_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Window_ChildLock_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val childlock_op = params?.getInt("childlock_op")
                val rearwindowcmd = params?.getInt("rearwindowcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor__898087379()
                data.BCM_Window_ChildLock_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Window_ChildLock_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Window_ChildLock_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( childlock_op != null ) {
                    data.BCM_Window_ChildLock_Ctrl_Enh.childlock_op = childlock_op.toUByte()
                }
                if( rearwindowcmd != null ) {
                    data.BCM_Window_ChildLock_Ctrl_Enh.rearwindowcmd = rearwindowcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, childlock_op = $childlock_op, rearwindowcmd = $rearwindowcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Window_ChildLock_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Door_ChildLock_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val childlock_op = params?.getInt("childlock_op")
                val reardoorcmd = params?.getInt("reardoorcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor_526666448()
                data.BCM_Door_ChildLock_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Door_ChildLock_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Door_ChildLock_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( childlock_op != null ) {
                    data.BCM_Door_ChildLock_Ctrl_Enh.childlock_op = childlock_op.toUByte()
                }
                if( reardoorcmd != null ) {
                    data.BCM_Door_ChildLock_Ctrl_Enh.reardoorcmd = reardoorcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, childlock_op = $childlock_op, reardoorcmd = $reardoorcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Door_ChildLock_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_CentralLock_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val centrallock_op = params?.getInt("centrallock_op")

                val data = HPCM_BCM_Service_eSrv.descriptor__1803624607()
                data.BCM_CentralLock_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_CentralLock_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_CentralLock_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( centrallock_op != null ) {
                    data.BCM_CentralLock_Ctrl_Enh.centrallock_op = centrallock_op.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, centrallock_op = $centrallock_op")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_CentralLock_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_DrvLock_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor__834664438()
                data.BCM_DrvLock_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_DrvLock_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_DrvLock_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_DrvLock_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_DrvLock_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_AwayLock_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor_1417955318()
                data.BCM_AwayLock_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_AwayLock_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_AwayLock_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_AwayLock_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_AwayLock_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_approachunlock_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor_978307593()
                data.BCM_approachunlock_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_approachunlock_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_approachunlock_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_approachunlock_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_approachunlock_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_SeatCS_Enh -> {

                // 设置parcelable 参数解析classLoader
                params?.classLoader = HPCC_SeatPosition::class.java.classLoader
                val bcm_callerid = params?.getInt("bcm_callerid")
                //获取参数
                val seatposition = params?.getParcelable<HPCC_SeatPosition>("seatposition")


                val data = HPCM_BCM_Service_eSrv.descriptor__392039341()
                data.BCM_SeatCS_Enh = Seres.HPCM_BCM_eSrv.BCM_SeatCS_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_SeatCS_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                var seatposition_trans:Seres.HPCM_BCM_eSrv.HPCC_SeatPosition = Seres.HPCM_BCM_eSrv.HPCC_SeatPosition()

                if (seatposition != null) {
                    val seat_fl = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_fl.mainxdir.toUByte()
                        mainzdir = seatposition.seat_fl.mainzdir.toUByte()
                        frontzdir = seatposition.seat_fl.frontzdir.toUByte()
                        backrestangle = seatposition.seat_fl.backrestangle.toUByte()
                        legrestangle = seatposition.seat_fl.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_fl.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_fl.footrestangle.toUByte()
                        frontxdir = seatposition.seat_fl.frontxdir.toUByte()

                    }
                    val seat_fr = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_fr.mainxdir.toUByte()
                        mainzdir = seatposition.seat_fr.mainzdir.toUByte()
                        frontzdir = seatposition.seat_fr.frontzdir.toUByte()
                        backrestangle = seatposition.seat_fr.backrestangle.toUByte()
                        legrestangle = seatposition.seat_fr.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_fr.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_fr.footrestangle.toUByte()
                        frontxdir = seatposition.seat_fr.frontxdir.toUByte()

                    }
                    val seat_rl = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_rl.mainxdir.toUByte()
                        mainzdir = seatposition.seat_rl.mainzdir.toUByte()
                        frontzdir = seatposition.seat_rl.frontzdir.toUByte()
                        backrestangle = seatposition.seat_rl.backrestangle.toUByte()
                        legrestangle = seatposition.seat_rl.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_rl.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_rl.footrestangle.toUByte()
                        frontxdir = seatposition.seat_rl.frontxdir.toUByte()

                    }
                    val seat_rm = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_rm.mainxdir.toUByte()
                        mainzdir = seatposition.seat_rm.mainzdir.toUByte()
                        frontzdir = seatposition.seat_rm.frontzdir.toUByte()
                        backrestangle = seatposition.seat_rm.backrestangle.toUByte()
                        legrestangle = seatposition.seat_rm.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_rm.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_rm.footrestangle.toUByte()
                        frontxdir = seatposition.seat_rm.frontxdir.toUByte()

                    }
                    val seat_rr = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_rr.mainxdir.toUByte()
                        mainzdir = seatposition.seat_rr.mainzdir.toUByte()
                        frontzdir = seatposition.seat_rr.frontzdir.toUByte()
                        backrestangle = seatposition.seat_rr.backrestangle.toUByte()
                        legrestangle = seatposition.seat_rr.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_rr.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_rr.footrestangle.toUByte()
                        frontxdir = seatposition.seat_rr.frontxdir.toUByte()

                    }
                    val seat_thirdl = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_thirdl.mainxdir.toUByte()
                        mainzdir = seatposition.seat_thirdl.mainzdir.toUByte()
                        frontzdir = seatposition.seat_thirdl.frontzdir.toUByte()
                        backrestangle = seatposition.seat_thirdl.backrestangle.toUByte()
                        legrestangle = seatposition.seat_thirdl.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_thirdl.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_thirdl.footrestangle.toUByte()
                        frontxdir = seatposition.seat_thirdl.frontxdir.toUByte()

                    }
                    val seat_thirdm = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_thirdm.mainxdir.toUByte()
                        mainzdir = seatposition.seat_thirdm.mainzdir.toUByte()
                        frontzdir = seatposition.seat_thirdm.frontzdir.toUByte()
                        backrestangle = seatposition.seat_thirdm.backrestangle.toUByte()
                        legrestangle = seatposition.seat_thirdm.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_thirdm.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_thirdm.footrestangle.toUByte()
                        frontxdir = seatposition.seat_thirdm.frontxdir.toUByte()

                    }
                    val seat_thirdr = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition().apply{
                        mainxdir = seatposition.seat_thirdr.mainxdir.toUByte()
                        mainzdir = seatposition.seat_thirdr.mainzdir.toUByte()
                        frontzdir = seatposition.seat_thirdr.frontzdir.toUByte()
                        backrestangle = seatposition.seat_thirdr.backrestangle.toUByte()
                        legrestangle = seatposition.seat_thirdr.legrestangle.toUByte()
                        legrestxdir = seatposition.seat_thirdr.legrestxdir.toUByte()
                        footrestangle = seatposition.seat_thirdr.footrestangle.toUByte()
                        frontxdir = seatposition.seat_thirdr.frontxdir.toUByte()

                    }
                    // 设置转换后的值
                    seatposition_trans.seat_fl = seat_fl
                    seatposition_trans.seat_fr = seat_fr
                    seatposition_trans.seat_rl = seat_rl
                    seatposition_trans.seat_rm = seat_rm
                    seatposition_trans.seat_rr = seat_rr
                    seatposition_trans.seat_thirdl = seat_thirdl
                    seatposition_trans.seat_thirdm = seat_thirdm
                    seatposition_trans.seat_thirdr = seat_thirdr
                }
                data.BCM_SeatCS_Enh.seatposition = seatposition_trans
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, seatposition = $seatposition")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_SeatCS_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_SideDoor_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val doorid = params?.getInt("doorid")
                val sidedoor_op = params?.getInt("sidedoor_op")

                val data = HPCM_BCM_Service_eSrv.descriptor_816261238()
                data.BCM_SideDoor_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_SideDoor_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_SideDoor_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( doorid != null ) {
                    data.BCM_SideDoor_Ctrl_Enh.doorid = doorid.toUByte()
                }
                if( sidedoor_op != null ) {
                    data.BCM_SideDoor_Ctrl_Enh.sidedoor_op = sidedoor_op.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, doorid = $doorid, sidedoor_op = $sidedoor_op")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_SideDoor_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Window_LockUp_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor_949607905()
                data.BCM_Window_LockUp_Enh = Seres.HPCM_BCM_eSrv.BCM_Window_LockUp_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Window_LockUp_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( onoffcmd != null ) {
                    data.BCM_Window_LockUp_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Window_LockUp_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Window_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val windowid = params?.getInt("windowid")
                val windowpos = params?.getInt("windowpos")

                val data = HPCM_BCM_Service_eSrv.descriptor__1717582714()
                data.BCM_Window_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Window_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Window_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( windowid != null ) {
                    data.BCM_Window_Ctrl_Enh.windowid = windowid.toUByte()
                }
                if( windowpos != null ) {
                    data.BCM_Window_Ctrl_Enh.windowpos = windowpos.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, windowid = $windowid, windowpos = $windowpos")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Window_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Wiper_Sensitivity_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val wiperid = params?.getInt("wiperid")
                val seneititylevel = params?.getInt("seneititylevel")

                val data = HPCM_BCM_Service_eSrv.descriptor__655106044()
                data.BCM_Wiper_Sensitivity_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Wiper_Sensitivity_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Wiper_Sensitivity_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( wiperid != null ) {
                    data.BCM_Wiper_Sensitivity_Ctrl_Enh.wiperid = wiperid.toUByte()
                }
                val cmd : CS_WiperSensitivityLevel = when(seneititylevel){
                    0x0 -> CS_WiperSensitivityLevel.CS_WiperSensitivityLevel_NO_REQUEST
                    0x1 -> CS_WiperSensitivityLevel.CS_WiperSensitivityLevel_LOW
                    0x2 -> CS_WiperSensitivityLevel.CS_WiperSensitivityLevel_MEDIUM
                    0x3 -> CS_WiperSensitivityLevel.CS_WiperSensitivityLevel_HIGH
                    else -> throw IllegalArgumentException("Invalid value for seneititylevel: $seneititylevel")
                }
                data.BCM_Wiper_Sensitivity_Ctrl_Enh.seneititylevel = cmd
                data._d = Hash_HPCM_BCM_Service_eSrv_BCM_Wiper_Sensitivity_Ctrl_Enh
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, wiperid = $wiperid, seneititylevel = $seneititylevel")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Wiper_Sensitivity_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCM_BCM_Service_eSrv_BCM_Wiper_Repair_Ctrl_Enh -> {

                val bcm_callerid = params?.getInt("bcm_callerid")
                val wiperid = params?.getInt("wiperid")
                val onoffcmd = params?.getInt("onoffcmd")

                val data = HPCM_BCM_Service_eSrv.descriptor_92092396()
                data.BCM_Wiper_Repair_Ctrl_Enh = Seres.HPCM_BCM_eSrv.BCM_Wiper_Repair_Ctrl_Enh_In()

                if( bcm_callerid != null ) {
                    data.BCM_Wiper_Repair_Ctrl_Enh.bcm_callerid = bcm_callerid.toUShort()
                }
                if( wiperid != null ) {
                    data.BCM_Wiper_Repair_Ctrl_Enh.wiperid = wiperid.toUByte()
                }
                if( onoffcmd != null ) {
                    data.BCM_Wiper_Repair_Ctrl_Enh.onoffcmd = onoffcmd.toUByte()
                }
                LogUtils.i(TAG, "serviceHashId = $serviceHashId bcm_callerid = $bcm_callerid, wiperid = $wiperid, onoffcmd = $onoffcmd")

                if(checkPubStatus("HPCM_BCM_Service_eSrv")){
                    ret = getPubWriterHpcmBcm().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCM_BCM_Service_eSrv_BCM_Wiper_Repair_Ctrl_Enh ret is : " + ret)
                }
            }
            ServiceHash.Hash_HPCC_ZCU_FR_Service_eSrv_RepairMode_setRepairMode -> {

                val repairmodestatus = params?.getInt("repairmodestatus")

                val data = HPCC_ZCU_FR_Service_eSrv.descriptor_349695082()
                data.RepairMode_setRepairMode = Seres.HPCC_ZCU_FR_eSrv.RepairMode_setRepairMode_In()

                val cmd : RepairModeStatus = when(repairmodestatus){
                    0x0 -> RepairModeStatus.RepairModeStatus_OFF
                    0x1 -> RepairModeStatus.RepairModeStatus_ON
                    0xFF -> RepairModeStatus.RepairModeStatus_INVALID
                    else -> throw IllegalArgumentException("Invalid value for repairmodestatus: $repairmodestatus")
                }
                data.RepairMode_setRepairMode.repairmodestatus = cmd
                data._d = Hash_HPCC_ZCU_FR_Service_eSrv_RepairMode_setRepairMode
                LogUtils.i(TAG, "serviceHashId = $serviceHashId repairmodestatus = $repairmodestatus")

                if(checkPubStatus("HPCC_ZCU_FR_Service_eSrv")){
                    ret = getPubWriterHpccZcuFr().write(data).toUByte()
                    LogUtils.i(TAG, "HASH_HPCC_ZCU_FR_Service_eSrv_RepairMode_setRepairMode ret is : " + ret)
                }
            }

        }
        return bundle
    }
}