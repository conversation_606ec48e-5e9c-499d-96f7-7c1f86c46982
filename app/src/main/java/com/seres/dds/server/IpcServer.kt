package com.seres.dds.server

import android.app.Service
import android.content.Intent
import android.os.Bundle
import android.os.IBinder
import com.seres.dds.commsvc.ipc.IpcClientManager.listenerRegister
import com.seres.dds.commsvc.ipc.IpcClientManager.listenerUnregister
//import com.seres.dds.database.DataBase.subscribeNode
//import com.seres.dds.database.DataBase.unsubscribeNode
import com.seres.dds.utils.LogUtils
import seres.s2s.internal.IAsyncResultCallback
import seres.s2s.internal.IS2SReportListener
import seres.s2s.internal.IS2SService
import java.util.concurrent.ConcurrentHashMap

val TAG = "IpcServer"

class IpcServer() : Service() {
    private val binder = object : IS2SService.Stub() {
        
        override fun registerS2SSignalListener(appId: Int, listener: IS2SReportListener?, initSignalHashIdList: IntArray?) {
            if (listener != null) {
                LogUtils.i(TAG,"AIDL - registerS2SSignalListener")

                listenerRegister(appId, listener, initSignalHashIdList)

            } else {
                LogUtils.w(TAG, "Register s2s data listener appId or listener can't be null.")
            }
        }
        
        override fun unregisterS2SSignalListener(appId: Int) {
            LogUtils.i(TAG,"AIDL - unregisterS2SSignalListener")
            listenerUnregister(appId)
        }
        
        override fun subS2SSignalListen(appId: Int, signalHashIdList: IntArray?) {
//            subscribeNode(appId,signalHashIdList)
        }
        
        override fun unsubS2SSignalListen(appId: Int, signalHashIdList: IntArray?) {
//            unsubscribeNode(appId,signalHashIdList)
        }
        
        override fun invoke(appId: Int, serviceHashId: Int, params: Bundle?): Bundle {
            LogUtils.i(TAG, "Client invoke serviceHashId=$serviceHashId, appId=$appId")
            return IpcServerHandler.handleRequestSync(appId, serviceHashId, params)
        }
        
        override fun invokeAsync(appId: Int, serviceHashId: Int, params: Bundle?, callback: IAsyncResultCallback?) {
            LogUtils.i(TAG, "Client invokeAsync methodName=$serviceHashId, appId=$appId")
            IpcServerHandler.handleRequestAsync(appId, serviceHashId, params, callback)
        }
    }
    
    override fun onBind(intent: Intent): IBinder? {
        return binder
    }
    
    companion object {
        // 存储客户端注册的数据上报监听器
        val dataListenerMap = ConcurrentHashMap<Int, IS2SReportListener>()
    }
}