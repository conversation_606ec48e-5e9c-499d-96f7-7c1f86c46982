package com.seres.dds.commsvc.pubsub

import Seres.HPCC_ZCU_FR_eSrv.HPCC_ZCU_FR_Service_eSrv
import Seres.HPCM_BCM_eSrv.HPCM_BCM_Service_eSrv
import com.seres.dds.sdk.core.StatusMask.Companion.publication_matched
import com.seres.dds.sdk.DataWriter
import com.seres.dds.sdk.DomainParticipant
import com.seres.dds.sdk.KScomNativeLib.kScomGetStatueMask
import com.seres.dds.sdk.KScomNativeLib.kScomSetStatusMask
import com.seres.dds.sdk.Publisher
import com.seres.dds.sdk.Topic
import com.seres.dds.sdk.core.Entity


object PubManager {
    const val TAG = "PubManager"

    private const val pubNameHpccZcuFRService:String = "HPCC_ZCU_FR_Service_eSrv"
    private var pubHpccZcuFRServiceReady:Boolean = false
    private var status_hpcc_zcu_fr_service:Long = 0
    private lateinit var writer_hpcc_zcu_fr:DataWriter

    private const val pubNameHpcmBcmService:String = "HPCM_BCM_Service_eSrv"
    private var pubHpcmBcmServiceReady:Boolean = false
    private var status_hpcm_bcm_service:Long = 0
    private lateinit var writer_hpcm_bcm_fr:DataWriter

    fun pubInit() {

        writer_hpcc_zcu_fr = createPubHpccZcufr()

        val threadHpccZcu = Thread{
            while(((status_hpcc_zcu_fr_service) and publication_matched().toLong()).toInt() == 0){
                status_hpcc_zcu_fr_service = kScomGetStatueMask(writer_hpcc_zcu_fr.ref)
                Thread.sleep(500)
                pubHpccZcuFRServiceReady = true
            }

        }
        threadHpccZcu.start()

        writer_hpcm_bcm_fr = createPubHpcmBcm()

        val threadHpcmBcm = Thread{
            while(((status_hpcm_bcm_service) and publication_matched().toLong()).toInt() == 0){
                status_hpcm_bcm_service = kScomGetStatueMask(writer_hpcm_bcm_fr.ref)
                Thread.sleep(500)
                pubHpcmBcmServiceReady = true
            }
        }
        threadHpcmBcm.start()
    }

    private fun createPubHpccZcufr() : DataWriter {

        val par = DomainParticipant(1)
        val publisher = Publisher(par)
        val topic_hpcm = Topic(par, pubNameHpccZcuFRService, HPCC_ZCU_FR_Service_eSrv())
        writer_hpcc_zcu_fr = DataWriter(par, topic_hpcm)

        kScomSetStatusMask(writer_hpcc_zcu_fr.ref, publication_matched().toLong())

        return writer_hpcc_zcu_fr
    }

    private fun createPubHpcmBcm() : DataWriter {

        val par = DomainParticipant(1)
        val publisher = Publisher(par)
        val topic_hpcm = Topic(par, pubNameHpcmBcmService, HPCM_BCM_Service_eSrv())
        writer_hpcm_bcm_fr = DataWriter(par, topic_hpcm)

        kScomSetStatusMask(writer_hpcm_bcm_fr.ref, publication_matched().toLong())

        return writer_hpcm_bcm_fr
    }

    fun getPubWriterHpccZcuFr():DataWriter{
        return writer_hpcc_zcu_fr
    }

    fun getPubWriterHpcmBcm():DataWriter{
        return writer_hpcm_bcm_fr
    }

    fun checkPubStatus(pubName:String) : Boolean{
        var ret : Boolean = false

        when(pubName){
            pubNameHpccZcuFRService -> ret =  pubHpccZcuFRServiceReady
            pubNameHpcmBcmService -> ret = pubHpcmBcmServiceReady
        }

        return  ret
    }

}