package com.seres.dds.commsvc.rpc

import Seres.BCM_eSrv.BCM_Service_eSrvClient
import com.seres.dds.commsvc.DomainContext
import com.seres.dds.sdk.ClientParam
import com.seres.dds.utils.LogUtils


object ClientManager {
    private const val TAG = "ClientManager"

    private const val serviceNameBCM:String = "BCM_Service_eSrv"
    private lateinit var clientBCM: BCM_Service_eSrvClient
    private var clientBCMReady:Boolean = false

    //init client
    fun clientInit()
    {
        clientBCM = createClientBCM()
        LogUtils.d(TAG, "Create client : $serviceNameBCM successful")

        val threadbcm = Thread{
            clientBCM.wait_for_service(1000000)
            clientBCMReady = true
        }
        threadbcm.start()       //启动线程
        LogUtils.d(TAG, "threadbcm start success ")

    }

    private fun createClientBCM():BCM_Service_eSrvClient
    {
        val param = ClientParam(DomainContext.dp(), null, null)
        param.set_service_name(serviceNameBCM)
        val client = BCM_Service_eSrvClient(param)
        return client
    }

    fun getClientBCM():BCM_Service_eSrvClient{
        return clientBCM
    }


    fun checkClientStatus(serviceName:String) : Boolean{
        var ret : Boolean = false

        when(serviceName){
            serviceNameBCM -> ret =  clientBCMReady
        }

        return  ret
    }
}