package com.seres.dds.commsvc.ipc

import android.os.Bundle
import com.seres.dds.server.api.HPCC_DoorMaxPosStatusInstance
import com.seres.dds.server.consts.InvokeConsts
import com.seres.dds.server.consts.SignalHash
import com.seres.dds.utils.LogUtils
import seres.s2s.internal.IS2SReportListener
import java.util.concurrent.ConcurrentHashMap

object IpcClientManager {
    private const val TAG = "IpcClientManager"
    private val dataListenerMap = ConcurrentHashMap<Int, IS2SReportListener>()
    private val bundleMap = ConcurrentHashMap<Int,ArrayList<Bundle>>()
    // 新增appid到信号列表的映射
    private val appIdToSignalIdsMap = ConcurrentHashMap<Int, List<Int>>()
    
    fun listenerRegister(appID:Int, listener: IS2SReportListener, array:IntArray?){

        dataListenerMap[appID] = listener
        bundleMap[appID] = ArrayList<Bundle>()

        // 保存appid与信号id列表的映射
        if (array != null) {
            appIdToSignalIdsMap[appID] = array.toList() // 转换为不可变列表
            LogUtils.i(TAG, "Register app[$appID] with ${array.size} signals")
        } else {
            appIdToSignalIdsMap.remove(appID)  // 如果 array 为 null，移除映射
            LogUtils.w(TAG, "Register app[$appID] with null signal array")
        }
        
        LogUtils.i(TAG,"Register app[${appID}] to S2sService")
        //这里会报错，暂时注释掉
//        subscribeNode(appID,array)
        
    }

    fun listenerUnregister(appID:Int){
        dataListenerMap.remove(appID)
        bundleMap.remove(appID)
        appIdToSignalIdsMap.remove(appID)  // 移除信号ID映射
        LogUtils.i(TAG,"Unregister app[${appID}] to S2sService")
    }

    // 新增：根据 appId 获取信号ID列表
    fun getSignalIdsForApp(appID: Int): List<Int>? {
        return appIdToSignalIdsMap[appID]
    }

    // 新增：检查 appId 是否关注某个信号
    fun isSignalMonitoredByApp(appID: Int, signalId: Int): Boolean {
        return appIdToSignalIdsMap[appID]?.contains(signalId) ?: false
    }
    
//    fun pushData(appID:Int,bundle: Bundle){
//        if(bundleMap[appID] != null)
//        {
//            bundleMap[appID]?.add(bundle)
//            LogUtils.i(TAG, "push bundle to app[${appID}]")
//        }
//        else
//        {
//            LogUtils.e(TAG, "app[id] unregister")
//        }
//    }
    
//    fun report(){
//        while (true){
//            Thread.sleep(50)
//
//            //Clear the list cache corresponding to all apps
//            bundleMap.forEach{(_,list) ->
//                list.clear()
//            }
//
//            //Traverse all the signals that have changed
//            for(key in enumValues<SignalHash>())
//            {
////                dbGetChangeNodeInfo(key.ordinal)
//            }
//
//            //report
//            bundleMap.forEach{(id,list) ->
//                if(list.isNotEmpty())
//                {
//                    val bundle = Bundle()
//                    bundle.putParcelableArray(InvokeConsts.KEY_CHANGE_PROP_LIST,list.toTypedArray())
//                    dataListenerMap[id]?.notify(bundle)
//                }
//            }
//        }
//
//    }

    fun reportTest(appID: Int, data:Int) {
        val bundle = Bundle()
        val hpcc_doormaxposstatusinstance = HPCC_DoorMaxPosStatusInstance(10,20)
        bundle.putInt("serviceId",700298604)
        bundle.putParcelable("doormaxpossts",hpcc_doormaxposstatusinstance)
        dataListenerMap[appID]?.notify(bundle)
    }
}