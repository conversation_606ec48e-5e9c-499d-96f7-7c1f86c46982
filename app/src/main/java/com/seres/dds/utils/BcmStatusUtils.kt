//package com.seres.dds.utils
//
//import com.seres.dds.database.DataBase
//import com.seres.dds.server.api.BCM_Status
//import seres.zcuf.OnOffCmd
//import seres.zcufr.BCM_Window_ActuateStatus
//import java.util.concurrent.ConcurrentLinkedQueue
//import java.util.concurrent.Executors
//import java.util.concurrent.ScheduledFuture
//import java.util.concurrent.TimeUnit
//
//object BcmStatusUtils {
//    private const val TAG = "BcmStatusUtils"
//    private val mThreadPool = Executors.newScheduledThreadPool(1)
//    private val sPool = ConcurrentLinkedQueue<BCM_Status>()
//    private var currentTask: ScheduledFuture<*>? = null
//
//    /**
//     * 获取BCMStatus对象
//     *
//     * @return
//     */
//    private fun obtainBcmStatus(): BCM_Status {
//        return sPool.poll() ?: BCM_Status()
//    }
//
//    /**
//     * 使用完成后，回收BcmStatus对象
//     *
//     * @param status
//     */
//    fun recycleBcmStatus(status: BCM_Status) {
//        status.set_actuateStatusFL(BCM_Window_ActuateStatus.UNKNOWN)
//        status.set_actuateStatusFR(BCM_Window_ActuateStatus.UNKNOWN)
//        status.set_ventilatingStatusFL(false)
//        status.set_ventilatingStatusFR(false)
//        status.set_commonlight_hood(OnOffCmd.NO_REQUEST.value)
//        status.set_commonlight_trunk_lightstatus(OnOffCmd.NO_REQUEST.value)
//        status.set_heatingLevelFL(0)
//        status.set_heatingLevelFR(0)
//        status.set_mainXDirFL(0)
//        status.set_mainXDirFR(0)
//        status.set_heatingStatusFL(false)
//        status.set_heatingStatusFR(false)
//        status.set_positionFL(0)
//        status.set_positionFR(0)
//        sPool.offer(status)
//    }
//
//    /**
//     * 取消打印BcmStatus的定时任务
//     *
//     */
//    fun cancelTask() {
//        LogUtils.i(TAG, "Cancel current schedule task")
//        currentTask?.cancel(true)
//    }
//
//    /**
//     * 执行定时打印BcmStatus的任务
//     *
//     * @param interval
//     * @param unit
//     * @param task
//     */
//    fun scheduleTask(interval: Long, unit: TimeUnit, task: Runnable) {
//        currentTask = mThreadPool.scheduleWithFixedDelay(task, 0, interval, unit)
//    }
//
//    /**
//     * 查询DataBase，获取当前BcmStatus各个字段数据
//     *
//     * @return
//     */
//    fun getCurrentBcmStatus(): BCM_Status {
//        val bcmStatus = obtainBcmStatus()
//        // hpcm
////        val hpcmStatus = DataBase.GetHpcmStatus()
////        bcmStatus.set_heatingStatusFL(hpcmStatus.getHeatingStatusFL() ?: false)
////        bcmStatus.set_heatingLevelFL(hpcmStatus.getHeatingLevelFL() ?: 0)
////        bcmStatus.set_ventilatingStatusFL(hpcmStatus.getVentilatingStatusFL() ?: false)
////        bcmStatus.set_heatingStatusFR(hpcmStatus.getHeatingStatusFR() ?: false)
////        bcmStatus.set_ventilatingStatusFR(hpcmStatus.getVentilatingStatusFR() ?: false)
////        bcmStatus.set_heatingLevelFR(hpcmStatus.getHeatingLevelFR() ?: 0)
////        bcmStatus.set_mainXDirFL(hpcmStatus.getMainXDirFL() ?: 0)
////        bcmStatus.set_mainXDirFR(hpcmStatus.getMainXDirFR() ?: 0)
//
//        //zcufr
////        val zcufrStatus = DataBase.GetZcuFRStatus()
////        bcmStatus.set_positionFL(zcufrStatus.getPositionFL() ?: 0)
////        bcmStatus.set_positionFR(zcufrStatus.getPositionFR() ?: 0)
////        bcmStatus.set_actuateStatusFL(zcufrStatus.getActuateStatusFL() ?: BCM_Window_ActuateStatus.IDLE)
////        bcmStatus.set_actuateStatusFR(zcufrStatus.getActuateStatusFR() ?: BCM_Window_ActuateStatus.UNKNOWN)
//
//        //zcuf
////        val zcufStatus = DataBase.GetZcuFStatus()
////        bcmStatus.set_commonlight_hood(zcufStatus.getCommonLightHood() ?: OnOffCmd.NO_REQUEST.value)
//        //zcur
////        val zcurStatus = DataBase.GetZcuRStatus()
////        bcmStatus.set_commonlight_trunk_lightstatus(zcurStatus.getCommonlightTrunkLightstatus() ?: OnOffCmd.NO_REQUEST.value)
//        return bcmStatus
//    }
//}