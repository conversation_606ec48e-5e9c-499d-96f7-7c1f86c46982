//package com.seres.dds.testdemo.zcufr_demo.rpc
//
//// import com.seres.dds.sdk.core.Listener
//import android.util.Log
//import com.seres.dds.sdk.DomainParticipant
//import com.seres.dds.sdk.Server
//import com.seres.dds.sdk.ServerParam
//import com.seres.dds.sdk.ServiceParam
//import seres.zcufr.*
//import seres.zcufr.ReturnCode
//
//
//class ServiceImpl : ZCUFR_Control_base() {
//
//    companion object {
//        private const val TAG = "ServiceImpl"
//    }
//
//    override fun BCM_Window_FL_AdjustPosition_Set(winPosCmd: Int): ReturnCode {
//        println("winPosCmd: "+ winPosCmd)
//        return ReturnCode.OK
//    }
//
//    override fun BCM_Window_FR_AdjustPosition_Set(winPosCmd: Int): ReturnCode {
//        println("winPosCmd: "+ winPosCmd)
//        return ReturnCode.OK    }
//
//    override fun BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel: Int): ReturnCode {
//        println("bcm_Legacy_SeatVentilationLevel: "+ bcm_Legacy_SeatVentilationLevel)
//        return ReturnCode.OK    }
//
//    override fun BCM_CushionTemp_FR_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel: Int): ReturnCode {
//        println("bcm_Legacy_SeatHeatingLevel: "+ bcm_Legacy_SeatHeatingLevel)
//        return ReturnCode.OK    }
//
//
//}
//
//class ServerDemo {
//    private var server: Server? = null
//    fun start() {
//        val dp = DomainParticipant(1)
//        server = Server(ServerParam(dp))
//        val serviceparam = ServiceParam(dp, null, null)
//        serviceparam.set_service_name("ZCU_FR_Service")
//        val service = ZCUFR_ControlService(ServiceImpl(), server!!, serviceparam)
//        server?.start()
//        Log.i(TAG, "end serverdemo")
//    }
//
//    fun stop() {
//        server?.stop()
//    }
//
//    companion object {
//        private const val TAG = "ServerDemo"
//    }
//
//}