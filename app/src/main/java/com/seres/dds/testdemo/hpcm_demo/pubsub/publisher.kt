//package com.seres.dds.testdemo.hpcm_demo.pubsub
//
//import Seres.HPCC_ZCU_FR_eSrv.HPCC_ZCU_FR_Service_eSrv
//import Seres.HPCC_ZCU_FR_eSrv.RepairMode_setRepairMode_In
//import com.seres.dds.sdk.DataWriter
//import com.seres.dds.sdk.DomainParticipant
//import com.seres.dds.sdk.KScomNativeLib.kScomSetStatusMask
//import com.seres.dds.sdk.Publisher
//import com.seres.dds.sdk.Topic
//import com.seres.dds.sdk.core.StatusMask.Companion.publication_matched
//import com.seres.dds.utils.LogUtils
//
//
//
//fun pubMain(){
//
//    val tag = "PubTest---   "
//    val par = DomainParticipant(1)
//    val publisher = Publisher(par)
//
//    println(tag + "enter pubMain success ")
//
//    val topic_hpcm = Topic(par, "HPCC_ZCU_FR_Service_eSrv", HPCC_ZCU_FR_Service_eSrv())
//
//    val writer_hpcm = DataWriter(par, topic_hpcm)
//    kScomSetStatusMask(writer_hpcm.ref, publication_matched().toLong());
//    var status_hpcm:Long = 0L
//
//    println(tag + "enter pubMain success 111")
//
//    while(((status_hpcm) and publication_matched().toLong()).toInt() == 0){
//        println(tag + "enter pubMain while success ")
//        Thread.sleep(500)
//
//        val data = HPCC_ZCU_FR_Service_eSrv()
//
//        data._d = 349695082
////        data._u_descriptor_349695082 = RepairMode_setRepairMode_In()
//
//        writer_hpcm.write(data)
//
//        LogUtils.d(tag,"pub msg to Sub")
//    }
//}

package com.seres.dds.testdemo.hpcm_demo.pubsub

import Seres.HPCC_ZCU_FR_eSrv.HPCC_ZCU_FR_Service_eSrv
//import Seres.HPCM_BCM_HPCC.HPCM_BCM_HPCC_Status
import com.seres.dds.sdk.DataWriter
import com.seres.dds.sdk.DomainParticipant
import com.seres.dds.sdk.KScomNativeLib.kScomGetStatueMask
import com.seres.dds.sdk.KScomNativeLib.kScomSetStatusMask
import com.seres.dds.sdk.Publisher
import com.seres.dds.sdk.Topic
import com.seres.dds.sdk.core.StatusMask.Companion.publication_matched
import com.seres.dds.utils.LogUtils


//class MyListener : Listener(){
//
//    override fun on_liveliness_changed(entityid:Int, dds_liveliness_changed_status:dds_liveliness_changed_status_t)
//    {
//        println("on_liveliness_changed ==> entityId = ${entityid}");
//    }
//
//    override fun on_publication_matched(entityid:Int, dds_publication_matched_status:dds_publication_matched_status_t)
//    {
//        println("on_publication_matched ==> entityId = ${entityid}");
//    }
//
//    override fun on_subscription_matched(entityid:Int, dds_subscription_matched_status:dds_subscription_matched_status_t)
//    {
//        println("on_subscription_matched ==> entityId = ${entityid}");
//    }
//}

var reportData = 0

fun pubMain(){

    val tag = "PubTest---   "
    val par = DomainParticipant(1)
    val publisher = Publisher(par)

    println(tag + "enter pubMain success ")

    val topic_hpcm = Topic(par, "HPCM_MCU_STATUS", HPCC_ZCU_FR_Service_eSrv())


    val writer_hpcm = DataWriter(par, topic_hpcm)
    kScomSetStatusMask(writer_hpcm.ref, publication_matched().toLong());
    var status_hpcm:Long = 0L

    println(tag + "enter pubMain success 111")

    while(((status_hpcm) and publication_matched().toLong()).toInt() == 0){
        status_hpcm = kScomGetStatueMask(writer_hpcm.ref)

        println(tag + "enter pubMain while success ")
        println(tag + "in line 101 status_hpcm is : " + status_hpcm)
        println(tag + "in line 102 publication_matched().toLong().toInt() is : " + publication_matched().toLong().toInt())
        println(tag + "in line 106 ((status_hpcm) and publication_matched().toLong()).toInt() is : " + ((status_hpcm) and publication_matched().toLong()).toInt())
//        status_hpcm = kScomGetStatueMask(writer_hpcm.ref)
        Thread.sleep(500)

        val data = HPCC_ZCU_FR_Service_eSrv()

//        data._d = 349695082

        writer_hpcm.write(data)

        LogUtils.d(tag,"pub msg to Sub")
    }

    println(tag + "in line 138 ((status_hpcm) and publication_matched().toLong()).toInt() is : " + ((status_hpcm) and publication_matched().toLong()).toInt())


}
