//package com.seres.dds.testdemo.hpcm_demo.rpc
//
//import android.util.Log
//import seres.hpcm.*
//import com.seres.dds.sdk.ClientParam
//import com.seres.dds.sdk.DataReader
//import com.seres.dds.sdk.DomainParticipant
//import com.seres.dds.sdk.Topic
//import java.util.concurrent.Executors
//
//class ClientDemo {
//    fun start() {
//        val dp = DomainParticipant(1)
//        val topic = Topic(dp, "HPCM_MCU_STATUS", HPCM_Status())
//        val reader = DataReader(dp, topic)
//
//        val task = Runnable {
//            val num = 0
//            while(true){
//                val samples = reader.take()
//                samples.sample_list!!.forEach{ sample ->
//                    var data = sample.type as seres.hpcm.HPCM_Status
//
//                    println("data.get_mainXDirFL: "+ data.get_mainXDirFL())
//                    println("data.get_mainXDirFR: "+ data.get_mainXDirFR())
//                    println("data.get_heatingLevelFL: " + data.get_heatingLevelFL())
//                    println("data.get_heatingStatusFR: " + data.get_heatingStatusFR())
//                    println("data.get_heatingLevelFR: " + data.get_heatingLevelFR())
//                    println("data.get_heatingLevelFL: " + data.get_heatingLevelFL())
//                    println("data.get_heatingStatusFL: " + data.get_heatingStatusFL())
//                    println("data.get_ventilatingStatusFL: "+ data.get_ventilatingStatusFL())
//                    println("data.get_ventilatingStatusFR: " + data.get_ventilatingStatusFR())
//                }
//
//                Thread.sleep(1000)
//            }
//        }
////
//
//        val fixedThreadPool = Executors.newFixedThreadPool(4)
//        val future = fixedThreadPool.submit(task)
//
//
//        val param = ClientParam(dp, null, null)
//        param.set_service_name("HPCM_MCU_Service")
//        val client = HPCM_ControlClient(param)
//        // 暂不实现client listener
//        // client.reply_datareader().clistener
//        Log.i(TAG, "client start to wait for service")
//        client.wait_for_service()
//
//        //
//        var num = 0
//        while(true){
//            num = (num+1)%5
//
//            val bcm_legacy_seatmainxdir : Int = num
//            val ret1 = client.BCM_Seat_FR_AdjustMainXDirCmdLeagacyECU(bcm_legacy_seatmainxdir)
//            Log.i(TAG, "client get retcode: "+ret1)
//
//            var bcm_Legacy_SeatVentilationLevel : Int = num
//            val ret2 = client.BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel)
//            Log.i(TAG, "client get retcode: "+ret2)
//
//
//            var bcm_Legacy_SeatHeatingLevel : Int = num
//            val ret3 = client.BCM_CushionTemp_FR_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel)
//            Log.i(TAG, "client get retcode: "+ret3)
//
//            val bcm_Legacy_SeatMainXDir : Int = num
//            val ret4 = client.BCM_Seat_FL_AdjustMainXDirCmdLeagacyECU(bcm_Legacy_SeatMainXDir)
//            Log.i(TAG, "client get retcode: "+ret4)
//
//            bcm_Legacy_SeatVentilationLevel = num
//            val ret5 = client.BCM_CushionTemp_FL_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatVentilationLevel)
//            Log.i(TAG, "client get retcode: "+ret5)
//
//            bcm_Legacy_SeatHeatingLevel= num
//            val ret6 = client.BCM_CushionTemp_FL_HeatCtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel)
//            Log.i(TAG, "client get retcode: "+ret6)
//
//        }
//    }
//
//    fun stop() {
//
//    }
//
//    companion object {
//        private const val TAG = "ClientDemo"
//    }
//}