//package com.seres.dds.testdemo.example
//
//import android.app.Service
//import android.content.Intent
//import android.os.IBinder
//import android.util.Log
//import com.seres.dds.example.IClientService
////import com.seres.dds.zcufr_demo.rpc.*
////import com.seres.dds.hpcm_demo.rpc.*
//import com.seres.dds.testdemo.hpcm_demo.rpc.ClientDemo
//import java.util.concurrent.Executors
//import java.util.concurrent.TimeUnit
//
//class ClientService : Service() {
//    private var clientDemo: ClientDemo? = null
//    private val threadPool = Executors.newScheduledThreadPool(1)
//
//    private val binder: IClientService.Stub = object : IClientService.Stub() {
//        override fun startService() {
//            Log.i(TAG, "ClientService start sub process")
//            startClient()
//        }
//    }
//
//    override fun onCreate() {
//        super.onCreate()
//        Log.i(TAG, "onCreate, process is = ${android.os.Process.myPid()}")
//    }
//
//    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
//        Log.i(TAG, "onStartCommand, process is = ${android.os.Process.myPid()}")
//        startClient()
//        return super.onStartCommand(intent, flags, startId)
//    }
//
//    override fun onBind(intent: Intent?): IBinder {
//        return binder
//    }
//
//    private fun startClient() {
//        threadPool.schedule(object : Runnable {
//            override fun run() {
//                clientDemo = ClientDemo()
//                clientDemo?.start()
//            }
//        }, 4, TimeUnit.SECONDS)
//    }
//
//    companion object {
//        const val TAG = "ClientService"
//    }
//}