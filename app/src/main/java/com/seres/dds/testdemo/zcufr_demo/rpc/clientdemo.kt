//package com.seres.dds.testdemo.zcufr_demo.rpc
//
//import android.util.Log
////import seres.hpcm.*
//import com.seres.dds.sdk.ClientParam
//import com.seres.dds.sdk.DomainParticipant
//import seres.zcufr.*
//
//class ClientDemo {
//    fun start() {
//        val dp = DomainParticipant(1)
//
//
//        val param = ClientParam(dp, null, null)
//        param.set_service_name("MCU_CTRL")
//        val client = ZCUFR_ControlClient(param)
//        // 暂不实现client listener
//        // client.reply_datareader().clistener
//        Log.i(TAG, "client start to wait for service")
//        client.wait_for_service()
//
//        while(true){
//            val bcm_legacy_seatmainxdir : Int = 123
//            val ret1 = client.BCM_Window_FL_AdjustPosition_Set(bcm_legacy_seatmainxdir)
//            Log.i(TAG, "client get retcode: "+ret1)
//
//            val bcm_Legacy_SeatVentilationLevel : Int = 321
//            val ret2 = client.BCM_Window_FR_AdjustPosition_Set(bcm_Legacy_SeatVentilationLevel)
//            Log.i(TAG, "client get retcode: "+ret2)
//
//
//            val bcm_Legacy_SeatHeatingLevel : Int = 456
//            val ret3 = client.BCM_CushionTemp_FR_Ventilation_CtrlLeagacyECU(bcm_Legacy_SeatHeatingLevel)
//            Log.i(TAG, "client get retcode: "+ret3)
//
//            val bcm_Legacy_SeatMainXDir : Int = 654
//            val ret4 = client.BCM_CushionTemp_FR_HeatCtrlLeagacyECU(bcm_Legacy_SeatMainXDir)
//            Log.i(TAG, "client get retcode: "+ret4)
//
//            Thread.sleep(1000)
//        }
//    }
//
//    fun stop() {
//
//    }
//
//    companion object {
//        private const val TAG = "ClientDemo"
//    }
//}