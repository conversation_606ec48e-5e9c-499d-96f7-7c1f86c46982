//package com.seres.dds.testdemo.hpcm_demo.pubsub
//
//import Seres.HPCC_ZCU_FR_eSrv.HPCC_ZCU_FR_Service_eSrv
//import com.seres.dds.sdk.DataReader
//import com.seres.dds.sdk.DomainParticipant
//import com.seres.dds.sdk.Topic
//
//val TAG  = "subscriber---   "
//
//fun subMain(){
//
//    println(TAG + "!!!!!!enter subMain success")
//
//    val par = DomainParticipant(1)
//    val topic = Topic(par, "HPCC_ZCU_FR_Service_eSrv", HPCC_ZCU_FR_Service_eSrv())
//    val reader = DataReader(par, topic)
//
//    println(TAG + "enter subMain success111")
//
//    while(true){
//        println(TAG + "enter subMain while success")
//
//        val samples = reader.take()
//
//        samples.sample_list!!.forEach{ sample ->
//
//            println(TAG + "enter subMain foreach success")
//
//            println(TAG + "bug in subMain in line 29 sample.type is :" + sample.type)
//
//            var data = sample.type as HPCC_ZCU_FR_Service_eSrv
//
//            println(TAG + "this is a bug test")
//            println(TAG + "data._d is " + data._d)
//
//
//        }
//        Thread.sleep(1000)
//    }
//}
//
//
//
package com.seres.dds.testdemo.hpcm_demo.pubsub

//import seres.hpcm.HPCM_Status
import Seres.HPCC_ZCU_FR_eSrv.HPCC_ZCU_FR_Service_eSrv
//import Seres.HPCM_BCM_HPCC.HPCM_BCM_HPCC_Status
import com.seres.dds.sdk.DataReader
import com.seres.dds.sdk.DomainParticipant
import com.seres.dds.sdk.KScomNativeLib.kScomGetStatueMask
import com.seres.dds.sdk.KScomNativeLib.kScomSetStatusMask
import com.seres.dds.sdk.Publisher
import com.seres.dds.sdk.Topic
import java.io.ByteArrayOutputStream
import java.io.ObjectOutputStream

val TAG  = "subscriber---   "

fun subMain(){

    println(TAG + "enter subMain success")

    val par = DomainParticipant(1)
//    val topic = Topic(par, "HPCM_MCU_STATUS", HPCM_Status())
    val topic = Topic(par, "HPCM_MCU_STATUS", HPCC_ZCU_FR_Service_eSrv())
    val reader = DataReader(par, topic)

    println(TAG + "enter subMain success111")

    while(true){
        println(TAG + "enter subMain while success222")

        val samples = reader.take()

        println(TAG + "bug in line 77 samples is : " + samples)
        println(TAG + "bug in line 78 samples.sample_list is : " + samples.sample_list)
        println(TAG + "bug in line 79 is : samples.sample_count " + samples.sample_count)

        samples.sample_list!!.forEach{ sample ->

            println(TAG + "enter subMain foreach success333")

            println(TAG + "sample.type  is : " + sample.type )

            var data = sample.type as HPCC_ZCU_FR_Service_eSrv

//            println(TAG + "data._d is : " + data._d)
            println(TAG + "data is : " + data)
        }
        Thread.sleep(1000)
    }
}

