package com.seres.dds

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import android.view.View
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.google.gson.GsonBuilder
import com.seres.dds.commsvc.ipc.IpcClientManager
import com.seres.dds.databinding.ActivityMainBinding
import com.seres.dds.example.IClientService
import com.seres.dds.s2sservicesdk.S2sServiceSDK
import com.seres.dds.server.CarControlManager
import com.seres.dds.server.api.HPCC_AmbRgbBrightnessCtrl
import com.seres.dds.server.api.HPCC_MusicAmbCtrl
import com.seres.dds.server.api.HPCC_MusicRhythmOp
import com.seres.dds.server.api.HPCC_SchroederLightCtrl
import com.seres.dds.server.consts.ServiceHash.BCM_MUSICRHYLIGHT_CTRL_ENH
import com.seres.dds.server.consts.ServiceHash.BCM_TAILGATE_SETMAXTARGETPOSITION_CTRL_ENH
import com.seres.dds.testdemo.example.SubService
import com.seres.dds.testdemo.hpcm_demo.pubsub.pubMain
import com.seres.dds.utils.LogUtils
import seres.s2s.internal.IS2SReportListener
import seres.s2s.internal.IS2SService
import java.util.concurrent.Executors


class MainActivity : AppCompatActivity() {
    private var binding: ActivityMainBinding? = null
    private val threadPool = Executors.newCachedThreadPool()
//    private var serverDemo: ServerDemo? = null
    private var clientService: IClientService? = null
    private val gson = GsonBuilder().setPrettyPrinting().create()
    private var mService: IS2SService? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化S2s所有服务
        S2sServiceSDK.init(this)
        
        binding = ActivityMainBinding.inflate(getLayoutInflater())
        setContentView(binding?.root)

        enableEdgeToEdge()
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
        initView()
    }
    
    private fun initView() {
        binding?.btnStartSubscriber?.setOnClickListener(object : View.OnClickListener {
            override fun onClick(v: View?) {
                threadPool.submit(object : Runnable {
                    override fun run() {
                        LogUtils.i(TAG, "Click sub main")
                        val intent = Intent(this@MainActivity, SubService::class.java)
                        LogUtils.i(TAG, "Click sub main111")
                        startService(intent)
                    }
                })
            }
        })
        binding?.btnStartPublisher?.setOnClickListener(object : View.OnClickListener {
            override fun onClick(v: View?) {
                threadPool.submit(object : Runnable {
                    override fun run() {
                        LogUtils.i(TAG, "Click pub main")
                        pubMain()
                    }
                })
            }
        })
        binding?.btnStartClient?.setOnClickListener(object : View.OnClickListener {
            override fun onClick(p0: View?) {
                LogUtils.i(TAG, "Click client start")
//                val intent = Intent(this@MainActivity, ClientService::class.java)
//                startService(intent)
            }
        })
        binding?.btnStartServer?.setOnClickListener {
            threadPool.submit(object : Runnable {
                override fun run() {
                    LogUtils.i(TAG, "Click server start")
//                    serverDemo = ServerDemo()
//                    serverDemo?.start()
                }
            })
        }
        
        binding?.btnStartPrintStatus?.setOnClickListener {
            startPrintBcmStatus()
        }
        binding?.btnStopPrintStatus?.setOnClickListener {
//            BcmStatusUtils.cancelTask()
        }

        // 注册监听
        binding?.bindService?.setOnClickListener{
            val intent = Intent()
            intent.setComponent(ComponentName(REMOTE_SERVER_PKG, REMOTE_SERVER_CLASS))
            bindService(intent, object : ServiceConnection {
                override fun onServiceConnected(name: ComponentName, service: IBinder) {
                    Log.e(TAG, "onServiceConnected ")
                    mService = IS2SService.Stub.asInterface(service)
                    //需要监听的信号id
                    val signals = mutableListOf(111,222)
                    mService?.registerS2SSignalListener(123,  object : IS2SReportListener.Stub() {
                        override fun notify(data: Bundle?) {
                            if (data != null) {
                                if (data.containsKey("code")) {
                                    val result = data.getInt("code")

                                    runOnUiThread {
                                        binding?.onMsg?.text = "收到的数据:$result"
                                    }
                                    Log.w(TAG, "Receive s2s report message is $result")
                                }
                            } else {
                                Log.w(TAG, "Receive s2s report message is null")
                            }
                        }
                    }, signals.toIntArray())
                }

                override fun onServiceDisconnected(name: ComponentName) {
                    Log.e(TAG, "onServiceDisconnected ")
                }
            }, Context.BIND_AUTO_CREATE)
        }

        //发送消息
        binding?.sendData?.setOnClickListener{
            IpcClientManager.reportTest(123,10086)
        }

        //CarControlManager接口使用示例 start
        binding?.initSdk?.setOnClickListener {
            CarControlManager.instance?.init(this)
        }

        binding?.sendDataToSoa?.setOnClickListener{
            val callerId = 123
            val serviceId = -1648589748
            val params = "{\n" +
                    "  \"doorid\": 1,\n" +
                    "  \"maxtargetposition\": 40\n" +
                    "}"
            val result = CarControlManager.instance?.invoke(callerId,serviceId,params)
        }

        binding?.sendDataToSoaAsync?.setOnClickListener{
            sendBaseDataDemo()
//            sendJsonDataDemo()
//            sendStructDataDemo()
        }
        //CarControlManager接口使用示例 end
    }

    /**
     * Send base data demo
     *
     */
    private fun sendBaseDataDemo() {
        val callerId = 123
        val serviceId1 = BCM_TAILGATE_SETMAXTARGETPOSITION_CTRL_ENH
        val bundle = Bundle()
        val hpcc_maxtargetposition = 10
        bundle.putInt("maxtargetposition", hpcc_maxtargetposition)
        Log.d(TAG, "test sendDataToSoaAsync : ${gson.toJson(bundle)}")
        CarControlManager.instance?.invokeAsync(
            callerId,
            serviceId1,
            bundle,
            object : CarControlManager.CmdEventCallback {
                override fun onResult(serviceId: Int, data: Bundle) {
                    Log.i(TAG, "onResult,serviceId :$serviceId ")
                }
            })
    }

    /**
     * Send json data demo
     *
     */
    private fun sendJsonDataDemo() {
        val callerId = 123
        val serviceId = BCM_MUSICRHYLIGHT_CTRL_ENH
        val paramJson = "{\n" +
                "  \"musicrhythmop\": {\n" +
                "    \"ambctrl\": {\n" +
                "      \"ambzoneid\": 10,\n" +
                "      \"onoffcmd\": 20\n" +
                "    },\n" +
                "    \"ambset\": {\n" +
                "      \"ambzoneid\": 10,\n" +
                "      \"brightness\": 20,\n" +
                "      \"rgb\": 30\n" +
                "    },\n" +
                "    \"schroederset\": {\n" +
                "      \"brightness\": 10,\n" +
                "      \"rgb\": 20\n" +
                "    }\n" +
                "  }\n" +
                "}"
        CarControlManager.instance?.invokeAsync(
            callerId,
            serviceId,
            paramJson,
            object : CarControlManager.CmdEventCallback {
                override fun onResult(serviceId: Int, data: Bundle) {
                    Log.i(TAG, "onResult json ,serviceId :$serviceId ")
                }
            })
    }

    /**
     * Send struct data demo
     *
     */
    private fun sendStructDataDemo() {
        val callerId = 123
        val serviceId = BCM_MUSICRHYLIGHT_CTRL_ENH
        val bundle = Bundle()
        val hpcc_musicrhythmop = HPCC_MusicRhythmOp(
            HPCC_MusicAmbCtrl(10,20)
            , HPCC_AmbRgbBrightnessCtrl(10,20,30)
            , HPCC_SchroederLightCtrl(10,20)
        )
        Log.d(TAG,"test sendDataToSoaAsync : ${ gson.toJson(hpcc_musicrhythmop)}")
        bundle.putParcelable("musicrhythmop",hpcc_musicrhythmop)
        CarControlManager.instance?.invokeAsync(callerId,serviceId,bundle,object  : CarControlManager.CmdEventCallback {
            override fun onResult(serviceId: Int, data: Bundle) {
                Log.i(TAG,"onResult,serviceId :$serviceId ")
            }
        })
    }

    private fun startPrintBcmStatus() {
//        BcmStatusUtils.cancelTask()
//        BcmStatusUtils.scheduleTask(20, TimeUnit.MILLISECONDS) {
//            val bcmStatus = BcmStatusUtils.getCurrentBcmStatus()
//            val bcmStatusJson = gson.toJson(bcmStatus)
//            LogUtils.i(TAG, "Current bcm status:${bcmStatusJson}")
//            runOnUiThread {
//                binding?.tvBtmStatus?.text = bcmStatusJson
//            }
//            BcmStatusUtils.recycleBcmStatus(bcmStatus)
//        }
    }
    
    companion object {
        private const val TAG = "MainActivity"
        private const val REMOTE_SERVER_PKG = "com.seres.dds"
        private const val REMOTE_SERVER_CLASS = "com.seres.dds.server.IpcServer"
    }
}