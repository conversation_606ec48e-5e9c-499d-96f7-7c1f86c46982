//package com.seres.dds.database.subdatabase
//
//import com.seres.dds.database.btree.S2sBTree
//import com.seres.dds.server.consts.SignalHash
//
//open class HpcmStatus {
//    //默认一组信号采用同一个信号周期
//    private var heatingStatusFL: Boolean = false //返回主驾加热状态
//    private var heatingLevelFL: Int = 0 //返回主驾加热等级
//    private var ventilatingStatusFL: Boolean = false //返回主驾通风状态
//    private var heatingStatusFR: Boolean = false //返回副驾加热状态
//    private var ventilatingStatusFR: Boolean = false //返回副驾通风状态
//    private var heatingLevelFR: Int = 0 //返回副驾加热等级
//    private var mainXDirFL: Int = 0 //返回主驾座椅位置
//    private var mainXDirFR: Int = 0 //返回副驾座椅位置
//    private var ventilationLevelFR: Int=0
//    private var ventilationLevelFL: Int=0
//
//    private var seatFLXActuateStatus : Int = 0
//    private var seatFRXActuateStatus : Int = 0
//
//    fun hpcmStatusNodeInit(){
//        S2sBTree.nodeCreate(SignalHash.heatingStatusFL_Hash.ordinal,Boolean::class.java, false)
//        S2sBTree.nodeCreate(SignalHash.heatingLevelFL_Hash.ordinal,Int::class.java, 333)
//        S2sBTree.nodeCreate(SignalHash.ventilatingStatusFL_Hash.ordinal,Boolean::class.java, false)
//        S2sBTree.nodeCreate(SignalHash.heatingStatusFR_Hash.ordinal,Boolean::class.java, false)
//        S2sBTree.nodeCreate(SignalHash.ventilatingStatusFR_Hash.ordinal,Boolean::class.java, false)
//        S2sBTree.nodeCreate(SignalHash.heatingLevelFR_Hash.ordinal,Int::class.java, 0)
//        S2sBTree.nodeCreate(SignalHash.mainXDirFL_Hash.ordinal,Int::class.java, 1)
//        S2sBTree.nodeCreate(SignalHash.mainXDirFR_Hash.ordinal,Int::class.java, 2)
//        S2sBTree.nodeCreate(SignalHash.ventilationFL_Hash.ordinal,Int::class.java, 0)
//        S2sBTree.nodeCreate(SignalHash.ventilationFR_Hash.ordinal,Int::class.java, 0)
//        S2sBTree.nodeCreate(SignalHash.seat_FL_XActuateStatus_Hash.ordinal,Int::class.java, 0)
//        S2sBTree.nodeCreate(SignalHash.seat_FR_XActuateStatus_Hash.ordinal,Int::class.java, 0)
//    }
//}