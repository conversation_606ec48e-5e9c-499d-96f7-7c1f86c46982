//package com.seres.dds.database
//
//import android.os.Bundle
//import com.seres.dds.commsvc.ipc.IpcClientManager.pushData
//import com.seres.dds.database.btree.Node
//import com.seres.dds.database.btree.S2sBTree.nodeCreate
//import com.seres.dds.database.btree.S2sBTree.nodeFind
//import com.seres.dds.database.subdatabase.HpcmStatus
//import com.seres.dds.database.subdatabase.ZcuFRStatus
//import com.seres.dds.database.subdatabase.ZcuFStatus
//import com.seres.dds.database.subdatabase.ZcuRStatus
//import com.seres.dds.server.consts.InvokeConsts
//import com.seres.dds.utils.ErrorCode
//import com.seres.dds.utils.LogUtils
//import java.util.concurrent.locks.ReentrantReadWriteLock
//import kotlin.random.Random
//
//object DataBase {
//    private const val TAG = "DataBase"
//    private val lock = ReentrantReadWriteLock()
//    private val readLock = lock.readLock()
//    private val writeLock = lock.writeLock()
//
//
//    fun init(){
//        HpcmStatus().hpcmStatusNodeInit()
//        ZcuFRStatus().zcufrStatusNodeInit()
//        ZcuFStatus().zcufStatusNodeInit()
//        ZcuRStatus().zcurStatusNodeInit()
//    }
//
//    private fun getType(value: Any?) : Class<*>?{
//        var type : Class<*>? = null
//        when(value)
//        {
//            is Byte -> {
//                type = Byte::class.java
//            }
//            is UByte -> {
//                type = UByte::class.java
//            }
//            is Short -> {
//                type = Short::class.java
//            }
//            is UShort -> {
//                type = UShort::class.java
//            }
//            is Int -> {
//                type = Int::class.java
//            }
//            is UInt -> {
//                type = UInt::class.java
//            }
//            is Long -> {
//                type = Long::class.java
//            }
//            is ULong -> {
//                type = ULong::class.java
//            }
//            is Float -> {
//                type = Float::class.java
//            }
//            is Double -> {
//                type = Double::class.java
//            }
//            is Char -> {
//                type = Char::class.java
//            }
//            is String -> {
//                type = String::class.java
//            }
//            is Boolean -> {
//                type = Boolean::class.java
//            }
//        }
//        return type
//    }
//
//    private fun dataCheck(node: Node<*>, inputValue: Any?) : Boolean{
//
//        //value check
//        readLock.lock()
//        val nodeValue = node.readNodeValue()
//        readLock.unlock()
//
//        if(nodeValue == inputValue){
//            return  false
//        }
//
//        return true
//    }
//
//    /**
//     * @name: setNodeValue
//     * @Description: write node info
//     * @param key: node index
//     * @param value: signal value
//     * @return: ErrorCode
//     */
//    fun <T>dbSetNodeValue(key:Int, value:T): ErrorCode {
//        var errorCode:ErrorCode = ErrorCode.EC_NO_ERROR
//        val node = nodeFind(key)
//
//        //The type of the value must be consistent with the type of the node.
//        if(getType(value) != node.getNodeType())
//        {
//            LogUtils.d(TAG,"input type != node type")
//            errorCode = ErrorCode.EC_TYPE_MISMATCH
//            return errorCode
//        }
//
//        //Update the database only when the value has changed.
//        if(!dataCheck(node,value))
//        {
//            LogUtils.d(TAG,"value no change,do nothing")
//            errorCode = ErrorCode.EC_DATA_NO_CHANGE
//            return errorCode
//        }
//
//        //Update database
//        writeLock.lock()
//        (node as Node<T>).writeNodeValue(value)
//        node.writeNodeIsChange(true)
//        writeLock.unlock()
//
//        return errorCode
//    }
//
//
//    /**
//     * @name: getNodeValue
//     * @Description: read node info
//     * @param key: node index
//     * @return: signal value
//     */
//    fun <T>dbGetNodeValue(key: Int):T{
//        val node = nodeFind(key)
//
//        readLock.lock()
//        val value = node.readNodeValue()
//        readLock.unlock()
//
//        //The caller needs to ensure the correctness of the type T.
//        //Otherwise, unknown errors may occur.
//        return value as T
//    }
//
//    /**
//     * @name: subscribeNode
//     * @Description: subscribe all node
//     * @param appId: app id
//     * @param nodeKeyList: The list of nodes that the app needs to subscribe to.
//     * @return: signal value
//     */
//    fun subscribeNode(appId: Int, nodeKeyList: IntArray?){
//        if(nodeKeyList != null)
//        {
//            for(key in nodeKeyList)
//            {
//                val node = nodeFind(key)
//
//                writeLock.lock()
//                node.subscribeApp(appId)
//                writeLock.unlock()
//
//                LogUtils.i(TAG,"app[${appId}] subscribe node[${key}]")
//            }
//        }
//    }
//
//    fun unsubscribeNode(appId: Int, nodeKeyList: IntArray?){
//        if(nodeKeyList != null)
//        {
//            for(key in nodeKeyList)
//            {
//                val node = nodeFind(key)
//
//                writeLock.lock()
//                node.unsubscribeApp(appId)
//                writeLock.unlock()
//                LogUtils.i(TAG,"app[${appId}] unsubscribe node[${key}]")
//            }
//        }
//    }
//
//    /**
//     * @name: dbGetChangeNodeInfo
//     * @Description: Package the node data where the data has changed
//     * @param key: node index
//     * @return:
//     */
//    fun dbGetChangeNodeInfo(key: Int){
//        val bundle = Bundle()
//        val status:Boolean
//        val node = nodeFind(key)
//
//        readLock.lock()
//        status = node.readNodeIsChange()
//        readLock.unlock()
//        if(status)
//        {
//            LogUtils.i(TAG,"signal [${key}] is changed")
//            readLock.lock()
//            val type = node.getNodeType()
//            val value = node.readNodeValue()
//            val list = node.getAppList()
//            readLock.unlock()
//
//            //put signal hash
//            bundle.putInt(InvokeConsts.KEY_CHANGE_PROP, key)
//            when(type){
//                Int::class.java -> {
//                    //put signal type
//                    bundle.putString(InvokeConsts.KEY_CHANGE_VALUE_TYPE, ValueType.INT.name)
//                    //put signal value
//                    bundle.putInt(InvokeConsts.KEY_CHANGE_PROP_VALUE, value as Int)
//                }
//                Boolean::class.java -> {
//                    //put signal type
//                    bundle.putString(InvokeConsts.KEY_CHANGE_VALUE_TYPE, ValueType.BOOLEAN.name)
//                    //put signal value
//                    bundle.putBoolean(InvokeConsts.KEY_CHANGE_PROP_VALUE, value as Boolean)
//                }
//                else -> {
//                    bundle.putString(InvokeConsts.KEY_CHANGE_VALUE_TYPE, ValueType.UNIT.name)
//                }
//            }
//
//            //遍历applist
//            list.forEach{appID ->
//                pushData(appID,bundle)
//            }
//
//            writeLock.lock()
//            node.writeNodeIsChange(false)
//            writeLock.unlock()
//        }
//    }
//
//    /* 测试 setNodeValue 接口的泛型强制转换*/
////    fun test(){
////        nodeCreate("Int",Int::class.java, 0)
////        nodeCreate("String",String::class.java, "hello")
////        nodeCreate("Boolean",Boolean::class.java, true)
////
////        //print node
////        LogUtils.i(TAG,"signalInt:"+ getNodeValue<Int>("Int"))
////        LogUtils.i(TAG,"signalString:"+ getNodeValue<String>("String"))
////        LogUtils.i(TAG,"signalBool:"+ getNodeValue<Boolean>("Boolean"))
////
////        //set node
////        setNodeValue<Int>("Int", 10)
////        LogUtils.i(TAG,"===signalInt change to:"+ getNodeValue<Int>("Int"))
////        setNodeValue<String>("String", "good")
////        LogUtils.i(TAG,"===signalString change to:"+ getNodeValue<String>("String"))
////        setNodeValue<Boolean>("Boolean", false)
////        LogUtils.i(TAG,"===signalBool change to:"+ getNodeValue<Boolean>("Boolean"))
////
////        //test1、更新数据库时，信号值未发生变化，预期不更新数据库
////        setNodeValue<Int>("Int", 10)
////        LogUtils.i(TAG,"---signalInt change to:"+ getNodeValue<Int>("Int"))
////
////        //test2、更新数据库时，输入类型与节点类型不匹配，预期应报错
////        setNodeValue<String>("Int", "Input type is string")
////        LogUtils.i(TAG,"---signalInt change to:"+ getNodeValue<Int>("Int"))
////
////        //test3、读取数据库时，存储变量与节点数据类型不一致会导致宕机
////        var buffer:Int? = getNodeValue<Int>("String")
////        LogUtils.i(TAG,"---buffer is:" + buffer)
////    }
//
//    /* 上电数据库建立耗时测试与node查询性能测试 */
//    fun timeTest(){
//        var startTime = System.currentTimeMillis()
//        nodeInsertTimeTest()
//        var endTime = System.currentTimeMillis()
//        var elapsedTime = endTime - startTime
//        LogUtils.i(TAG,"create Btree time: $elapsedTime ms")
//
//        startTime = System.currentTimeMillis()
//        nodeFindTimeTest()
//        endTime = System.currentTimeMillis()
//        elapsedTime = endTime - startTime
//        LogUtils.i(TAG,"node-find time: $elapsedTime ms")
//    }
//    private fun nodeInsertTimeTest(){
//        for (i in 1..2000)
//        {
//            if(i == 500)
//                LogUtils.d(TAG,"500 signals have been inserted.")
//            if(i == 1000)
//                LogUtils.d(TAG,"1000 signals have been inserted.")
//            if(i == 1500)
//                LogUtils.d(TAG,"1500 signals have been inserted.")
//            if(i == 2000)
//                LogUtils.d(TAG,"2000 signals have been inserted.")
//
//            nodeCreate(i, Int::class.java, i)
//        }
//    }
//    fun nodeFindTimeTest(){
//        for(i in 0..100)
//        {
//            val randomInt = Random.nextInt(2000)
//
//            if(dbGetNodeValue<Int>(randomInt) != null)
//            {
////                LogUtils.i(TAG,"signalInt:"+getNodeValue<Int>(randomInt.toString()))
//            }
//        }
//    }
//}
//
//
