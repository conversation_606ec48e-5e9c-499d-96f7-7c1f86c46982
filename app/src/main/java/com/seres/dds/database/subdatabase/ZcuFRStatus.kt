//package com.seres.dds.database.subdatabase
//
//import com.seres.dds.database.btree.S2sBTree
//import com.seres.dds.server.consts.SignalHash
//import seres.zcufr.BCM_Window_ActuateStatus
//
//class ZcuFRStatus {
//    private var positionFL: Int =0
//    private var positionFR: Int =0
//    private var heatingStatusFL: Boolean =false
//    private var heatingLevelFL: Int =0
//    private var ventilatingStatusFL: Boolean =false
//    private var heatingStatusFR: Boolean =false
//    private var ventilatingStatusFR: Boolean =false
//    private var heatingLevelFR: Int =0
//
//    private var actuatestatusfl: BCM_Window_ActuateStatus=BCM_Window_ActuateStatus.IDLE
//    private var actuatestatusfr: BCM_Window_ActuateStatus=BCM_Window_ActuateStatus.IDLE
//
//    fun zcufrStatusNodeInit(){
//       S2sBTree.nodeCreate(SignalHash.positionFL_Hash.ordinal,Int::class.java, 0)
//       S2sBTree.nodeCreate(SignalHash.positionFR_Hash.ordinal,Int::class.java, 0)
//    }
//}