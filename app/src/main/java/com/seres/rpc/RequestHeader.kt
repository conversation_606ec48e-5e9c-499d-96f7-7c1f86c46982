package dds.rpc


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class RequestHeader : TypeStruct() {
    private var _requestId : dds.rpc.SampleIdentity = dds.rpc.SampleIdentity()

    init{
        keyless = true
        version_support = 1
        typename = "dds::rpc::RequestHeader"
        orderedMembers = arrayListOf(
            Member("_requestId", false),
        )

        initproperty()
    }

    var requestId: dds.rpc.SampleIdentity
        get() = _requestId
        set(value){
            _requestId = value
        }

    fun copy(value: RequestHeader = this): RequestHeader{
            this._requestId =  value._requestId
            return this
        }

    override fun toString(): String{
        return "$typename(requestId=$requestId)"
    }
}

