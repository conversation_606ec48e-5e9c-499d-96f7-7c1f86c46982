package dds.rpc


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class EntityId_t : TypeStruct() {
    private var _entityKey : ArrayList<Byte> = ArrayList<Byte>(3)
    private var _entityKind : Byte = 0

    init{
        keyless = true
        version_support = 1
        typename = "dds::rpc::EntityId_t"
        orderedMembers = arrayListOf(
            Member("_entityKey", false, length = 3),
            Member("_entityKind", false),
        )

        initproperty()
    }

    var entityKey: ArrayList<Byte>
        get() = _entityKey
        set(value){
            _entityKey = value.take(3).toCollection(ArrayList<Byte>());
        }

    var entityKind: Byte
        get() = _entityKind
        set(value){
            _entityKind = value
        }

    fun copy(value: EntityId_t = this): EntityId_t{
            this._entityKey =  value._entityKey
            this._entityKind =  value._entityKind
            return this
        }

    override fun toString(): String{
        return "$typename(entityKey=$entityKey, entityKind=$entityKind)"
    }
}

