package dds.rpc


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class GUID_t : TypeStruct() {
    private var _guidPrefix : ArrayList<Byte> = ArrayList<Byte>(12)
    private var _entityId : dds.rpc.EntityId_t = dds.rpc.EntityId_t()

    init{
        keyless = true
        version_support = 1
        typename = "dds::rpc::GUID_t"
        orderedMembers = arrayListOf(
            Member("_guidPrefix", false, length = 12),
            Member("_entityId", false),
        )

        initproperty()
    }

    var guidPrefix: ArrayList<Byte>
        get() = _guidPrefix
        set(value){
            _guidPrefix = value.take(12).toCollection(ArrayList<Byte>());
        }

    var entityId: dds.rpc.EntityId_t
        get() = _entityId
        set(value){
            _entityId = value
        }

    fun copy(value: GUID_t = this): GUID_t{
            this._guidPrefix =  value._guidPrefix
            this._entityId =  value._entityId
            return this
        }

    override fun toString(): String{
        return "$typename(guidPrefix=$guidPrefix, entityId=$entityId)"
    }
}

