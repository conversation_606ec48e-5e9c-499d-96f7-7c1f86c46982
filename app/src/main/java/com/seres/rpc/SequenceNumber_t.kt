package dds.rpc


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class SequenceNumber_t : TypeStruct() {
    private var _high : Int = 0
    private var _low : UInt = 0u

    init{
        keyless = true
        version_support = 1
        typename = "dds::rpc::SequenceNumber_t"
        orderedMembers = arrayListOf(
            Member("_high", false),
            Member("_low", false),
        )

        initproperty()
    }

    var high: Int
        get() = _high
        set(value){
            _high = value
        }

    var low: UInt
        get() = _low
        set(value){
            _low = value
        }

    fun copy(value: SequenceNumber_t = this): SequenceNumber_t{
            this._high =  value._high
            this._low =  value._low
            return this
        }

    override fun toString(): String{
        return "$typename(high=$high, low=$low)"
    }
}

