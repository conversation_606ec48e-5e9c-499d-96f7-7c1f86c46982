package dds.rpc


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class ReplyHeader : TypeStruct() {
    private var _relatedRequestId : dds.rpc.SampleIdentity = dds.rpc.SampleIdentity()

    init{
        keyless = true
        version_support = 1
        typename = "dds::rpc::ReplyHeader"
        orderedMembers = arrayListOf(
            Member("_relatedRequestId", false),
        )

        initproperty()
    }

    var relatedRequestId: dds.rpc.SampleIdentity
        get() = _relatedRequestId
        set(value){
            _relatedRequestId = value
        }

    fun copy(value: ReplyHeader = this): ReplyHeader{
            this._relatedRequestId =  value._relatedRequestId
            return this
        }

    override fun toString(): String{
        return "$typename(relatedRequestId=$relatedRequestId)"
    }
}

