package dds.rpc


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class SampleIdentity : TypeStruct() {
    private var _writer_guid : dds.rpc.GUID_t = dds.rpc.GUID_t()
    private var _sequence_number : dds.rpc.SequenceNumber_t = dds.rpc.SequenceNumber_t()

    init{
        keyless = true
        version_support = 1
        typename = "dds::rpc::SampleIdentity"
        orderedMembers = arrayListOf(
            Member("_writer_guid", false),
            Member("_sequence_number", false),
        )

        initproperty()
    }

    var writer_guid: dds.rpc.GUID_t
        get() = _writer_guid
        set(value){
            _writer_guid = value
        }

    var sequence_number: dds.rpc.SequenceNumber_t
        get() = _sequence_number
        set(value){
            _sequence_number = value
        }

    fun copy(value: SampleIdentity = this): SampleIdentity{
            this._writer_guid =  value._writer_guid
            this._sequence_number =  value._sequence_number
            return this
        }

    override fun toString(): String{
        return "$typename(writer_guid=$writer_guid, sequence_number=$sequence_number)"
    }
}

