package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_CentralLock_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.HPCM_BCM_eSrv.BCM_CallerID = 0u
    private var _centrallock_op : Seres.HPCM_BCM_eSrv.HPCC_LockUnLockPara = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::BCM_CentralLock_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_centrallock_op", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.HPCM_BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var centrallock_op: Seres.HPCM_BCM_eSrv.HPCC_LockUnLockPara
        get() = _centrallock_op
        set(value){
            _centrallock_op = value
        }

    fun copy(value: BCM_CentralLock_Ctrl_Enh_In = this): BCM_CentralLock_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._centrallock_op =  value._centrallock_op
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, centrallock_op=$centrallock_op)"
    }
}

