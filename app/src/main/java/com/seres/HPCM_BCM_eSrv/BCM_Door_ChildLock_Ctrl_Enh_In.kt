package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Door_ChildLock_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.HPCM_BCM_eSrv.BCM_CallerID = 0u
    private var _childlock_op : Seres.HPCM_BCM_eSrv.HPCC_LockUnLockPara = 0u
    private var _reardoorcmd : Seres.HPCM_BCM_eSrv.HPCC_DoorID = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::BCM_Door_ChildLock_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_childlock_op", false),
            Member("_reardoorcmd", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.HPCM_BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var childlock_op: Seres.HPCM_BCM_eSrv.HPCC_LockUnLockPara
        get() = _childlock_op
        set(value){
            _childlock_op = value
        }

    var reardoorcmd: Seres.HPCM_BCM_eSrv.HPCC_DoorID
        get() = _reardoorcmd
        set(value){
            _reardoorcmd = value
        }

    fun copy(value: BCM_Door_ChildLock_Ctrl_Enh_In = this): BCM_Door_ChildLock_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._childlock_op =  value._childlock_op
            this._reardoorcmd =  value._reardoorcmd
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, childlock_op=$childlock_op, reardoorcmd=$reardoorcmd)"
    }
}

