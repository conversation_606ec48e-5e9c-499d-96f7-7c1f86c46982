package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


open class HPCM_BCM_Service_eSrv : TypeUnion(){

    protected var __d: Int = 0
    protected var __u: Any? = null

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::HPCM_BCM_Service_eSrv"
        dmutableMap.put(1656882926, descriptor_1656882926::class)
        dmutableMap.put(-920425368, descriptor__920425368::class)
        dmutableMap.put(1822396041, descriptor_1822396041::class)
        dmutableMap.put(1856911191, descriptor_1856911191::class)
        dmutableMap.put(66238767, descriptor_66238767::class)
        dmutableMap.put(655960075, descriptor_655960075::class)
        dmutableMap.put(505398926, descriptor_505398926::class)
        dmutableMap.put(207009957, descriptor_207009957::class)
        dmutableMap.put(-763709823, descriptor__763709823::class)
        dmutableMap.put(-1478517379, descriptor__1478517379::class)
        dmutableMap.put(-950422755, descriptor__950422755::class)
        dmutableMap.put(-1403693521, descriptor__1403693521::class)
        dmutableMap.put(100069557, descriptor_100069557::class)
        dmutableMap.put(-1750758886, descriptor__1750758886::class)
        dmutableMap.put(1305593815, descriptor_1305593815::class)
        dmutableMap.put(-1694013410, descriptor__1694013410::class)
        dmutableMap.put(-1484169858, descriptor__1484169858::class)
        dmutableMap.put(1826448851, descriptor_1826448851::class)
        dmutableMap.put(-986204298, descriptor__986204298::class)
        dmutableMap.put(-898087379, descriptor__898087379::class)
        dmutableMap.put(526666448, descriptor_526666448::class)
        dmutableMap.put(-1803624607, descriptor__1803624607::class)
        dmutableMap.put(-834664438, descriptor__834664438::class)
        dmutableMap.put(1417955318, descriptor_1417955318::class)
        dmutableMap.put(978307593, descriptor_978307593::class)
        dmutableMap.put(-392039341, descriptor__392039341::class)
        dmutableMap.put(816261238, descriptor_816261238::class)
        dmutableMap.put(949607905, descriptor_949607905::class)
        dmutableMap.put(-1717582714, descriptor__1717582714::class)
        dmutableMap.put(-655106044, descriptor__655106044::class)
        dmutableMap.put(92092396, descriptor_92092396::class)
    }

    var _d : Int
        get() = __d
        set(value) {
            __d = value
        }

    class descriptor_1656882926() : HPCM_BCM_Service_eSrv(){
        private var _VMM_UsageMode_EnterUsageMode: Seres.HPCM_BCM_eSrv.VMM_UsageMode_EnterUsageMode_In = Seres.HPCM_BCM_eSrv.VMM_UsageMode_EnterUsageMode_In()

        init{
            _d = 1656882926
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_VMM_UsageMode_EnterUsageMode", false))

            __u = this

            initproperty()
        }

        var VMM_UsageMode_EnterUsageMode: Seres.HPCM_BCM_eSrv.VMM_UsageMode_EnterUsageMode_In
            get() = _VMM_UsageMode_EnterUsageMode
            set(value) {
                _VMM_UsageMode_EnterUsageMode = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, VMM_UsageMode_EnterUsageMode=$VMM_UsageMode_EnterUsageMode)"
        }

    }

    class descriptor__920425368() : HPCM_BCM_Service_eSrv(){
        private var _VMM_UsageMode_SetExitDelayTi: Seres.HPCM_BCM_eSrv.VMM_UsageMode_SetExitDelayTi_In = Seres.HPCM_BCM_eSrv.VMM_UsageMode_SetExitDelayTi_In()

        init{
            _d = -920425368
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_VMM_UsageMode_SetExitDelayTi", false))

            __u = this

            initproperty()
        }

        var VMM_UsageMode_SetExitDelayTi: Seres.HPCM_BCM_eSrv.VMM_UsageMode_SetExitDelayTi_In
            get() = _VMM_UsageMode_SetExitDelayTi
            set(value) {
                _VMM_UsageMode_SetExitDelayTi = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, VMM_UsageMode_SetExitDelayTi=$VMM_UsageMode_SetExitDelayTi)"
        }

    }

    class descriptor_1822396041() : HPCM_BCM_Service_eSrv(){
        private var _VMM_CarMode_EnterCarMode: Seres.HPCM_BCM_eSrv.VMM_CarMode_EnterCarMode_In = Seres.HPCM_BCM_eSrv.VMM_CarMode_EnterCarMode_In()

        init{
            _d = 1822396041
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_VMM_CarMode_EnterCarMode", false))

            __u = this

            initproperty()
        }

        var VMM_CarMode_EnterCarMode: Seres.HPCM_BCM_eSrv.VMM_CarMode_EnterCarMode_In
            get() = _VMM_CarMode_EnterCarMode
            set(value) {
                _VMM_CarMode_EnterCarMode = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, VMM_CarMode_EnterCarMode=$VMM_CarMode_EnterCarMode)"
        }

    }

    class descriptor_1856911191() : HPCM_BCM_Service_eSrv(){
        private var _VMM_PowerMode_setPowerMode: Seres.HPCM_BCM_eSrv.VMM_PowerMode_setPowerMode_In = Seres.HPCM_BCM_eSrv.VMM_PowerMode_setPowerMode_In()

        init{
            _d = 1856911191
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_VMM_PowerMode_setPowerMode", false))

            __u = this

            initproperty()
        }

        var VMM_PowerMode_setPowerMode: Seres.HPCM_BCM_eSrv.VMM_PowerMode_setPowerMode_In
            get() = _VMM_PowerMode_setPowerMode
            set(value) {
                _VMM_PowerMode_setPowerMode = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, VMM_PowerMode_setPowerMode=$VMM_PowerMode_setPowerMode)"
        }

    }

    class descriptor_66238767() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Light_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Light_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Light_Ctrl_Enh_In()

        init{
            _d = 66238767
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Light_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Light_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Light_Ctrl_Enh_In
            get() = _BCM_Light_Ctrl_Enh
            set(value) {
                _BCM_Light_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Light_Ctrl_Enh=$BCM_Light_Ctrl_Enh)"
        }

    }

    class descriptor_655960075() : HPCM_BCM_Service_eSrv(){
        private var _BCM_ADBLight_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_ADBLight_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_ADBLight_Ctrl_Enh_In()

        init{
            _d = 655960075
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_ADBLight_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_ADBLight_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_ADBLight_Ctrl_Enh_In
            get() = _BCM_ADBLight_Ctrl_Enh
            set(value) {
                _BCM_ADBLight_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_ADBLight_Ctrl_Enh=$BCM_ADBLight_Ctrl_Enh)"
        }

    }

    class descriptor_505398926() : HPCM_BCM_Service_eSrv(){
        private var _BCM_PosnLampEnhance_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_PosnLampEnhance_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_PosnLampEnhance_Ctrl_Enh_In()

        init{
            _d = 505398926
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_PosnLampEnhance_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_PosnLampEnhance_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_PosnLampEnhance_Ctrl_Enh_In
            get() = _BCM_PosnLampEnhance_Ctrl_Enh
            set(value) {
                _BCM_PosnLampEnhance_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_PosnLampEnhance_Ctrl_Enh=$BCM_PosnLampEnhance_Ctrl_Enh)"
        }

    }

    class descriptor_207009957() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Light_BeamPosition_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Light_BeamPosition_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Light_BeamPosition_Ctrl_Enh_In()

        init{
            _d = 207009957
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Light_BeamPosition_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Light_BeamPosition_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Light_BeamPosition_Ctrl_Enh_In
            get() = _BCM_Light_BeamPosition_Ctrl_Enh
            set(value) {
                _BCM_Light_BeamPosition_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Light_BeamPosition_Ctrl_Enh=$BCM_Light_BeamPosition_Ctrl_Enh)"
        }

    }

    class descriptor__763709823() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Light_WidthGuidance_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Light_WidthGuidance_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Light_WidthGuidance_Ctrl_Enh_In()

        init{
            _d = -763709823
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Light_WidthGuidance_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Light_WidthGuidance_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Light_WidthGuidance_Ctrl_Enh_In
            get() = _BCM_Light_WidthGuidance_Ctrl_Enh
            set(value) {
                _BCM_Light_WidthGuidance_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Light_WidthGuidance_Ctrl_Enh=$BCM_Light_WidthGuidance_Ctrl_Enh)"
        }

    }

    class descriptor__1478517379() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Light_LowBeamEnhance_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Light_LowBeamEnhance_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Light_LowBeamEnhance_Ctrl_Enh_In()

        init{
            _d = -1478517379
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Light_LowBeamEnhance_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Light_LowBeamEnhance_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Light_LowBeamEnhance_Ctrl_Enh_In
            get() = _BCM_Light_LowBeamEnhance_Ctrl_Enh
            set(value) {
                _BCM_Light_LowBeamEnhance_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Light_LowBeamEnhance_Ctrl_Enh=$BCM_Light_LowBeamEnhance_Ctrl_Enh)"
        }

    }

    class descriptor__950422755() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Light_AFS_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Light_AFS_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Light_AFS_Ctrl_Enh_In()

        init{
            _d = -950422755
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Light_AFS_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Light_AFS_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Light_AFS_Ctrl_Enh_In
            get() = _BCM_Light_AFS_Ctrl_Enh
            set(value) {
                _BCM_Light_AFS_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Light_AFS_Ctrl_Enh=$BCM_Light_AFS_Ctrl_Enh)"
        }

    }

    class descriptor__1403693521() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Fog_Light_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Fog_Light_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Fog_Light_Ctrl_Enh_In()

        init{
            _d = -1403693521
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Fog_Light_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Fog_Light_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Fog_Light_Ctrl_Enh_In
            get() = _BCM_Fog_Light_Ctrl_Enh
            set(value) {
                _BCM_Fog_Light_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Fog_Light_Ctrl_Enh=$BCM_Fog_Light_Ctrl_Enh)"
        }

    }

    class descriptor_100069557() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Extrlight_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Extrlight_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Extrlight_Ctrl_Enh_In()

        init{
            _d = 100069557
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Extrlight_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Extrlight_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Extrlight_Ctrl_Enh_In
            get() = _BCM_Extrlight_Ctrl_Enh
            set(value) {
                _BCM_Extrlight_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Extrlight_Ctrl_Enh=$BCM_Extrlight_Ctrl_Enh)"
        }

    }

    class descriptor__1750758886() : HPCM_BCM_Service_eSrv(){
        private var _BCM_PosnLampDispPattern_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_PosnLampDispPattern_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_PosnLampDispPattern_Ctrl_Enh_In()

        init{
            _d = -1750758886
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_PosnLampDispPattern_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_PosnLampDispPattern_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_PosnLampDispPattern_Ctrl_Enh_In
            get() = _BCM_PosnLampDispPattern_Ctrl_Enh
            set(value) {
                _BCM_PosnLampDispPattern_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_PosnLampDispPattern_Ctrl_Enh=$BCM_PosnLampDispPattern_Ctrl_Enh)"
        }

    }

    class descriptor_1305593815() : HPCM_BCM_Service_eSrv(){
        private var _BCM_PosnLampStateDispPattern_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_PosnLampStateDispPattern_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_PosnLampStateDispPattern_Ctrl_Enh_In()

        init{
            _d = 1305593815
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_PosnLampStateDispPattern_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_PosnLampStateDispPattern_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_PosnLampStateDispPattern_Ctrl_Enh_In
            get() = _BCM_PosnLampStateDispPattern_Ctrl_Enh
            set(value) {
                _BCM_PosnLampStateDispPattern_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_PosnLampStateDispPattern_Ctrl_Enh=$BCM_PosnLampStateDispPattern_Ctrl_Enh)"
        }

    }

    class descriptor__1694013410() : HPCM_BCM_Service_eSrv(){
        private var _BCM_BatteryDispPattern_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_BatteryDispPattern_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_BatteryDispPattern_Ctrl_Enh_In()

        init{
            _d = -1694013410
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_BatteryDispPattern_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_BatteryDispPattern_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_BatteryDispPattern_Ctrl_Enh_In
            get() = _BCM_BatteryDispPattern_Ctrl_Enh
            set(value) {
                _BCM_BatteryDispPattern_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_BatteryDispPattern_Ctrl_Enh=$BCM_BatteryDispPattern_Ctrl_Enh)"
        }

    }

    class descriptor__1484169858() : HPCM_BCM_Service_eSrv(){
        private var _BCM_ThanksDisp_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_ThanksDisp_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_ThanksDisp_Ctrl_Enh_In()

        init{
            _d = -1484169858
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_ThanksDisp_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_ThanksDisp_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_ThanksDisp_Ctrl_Enh_In
            get() = _BCM_ThanksDisp_Ctrl_Enh
            set(value) {
                _BCM_ThanksDisp_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_ThanksDisp_Ctrl_Enh=$BCM_ThanksDisp_Ctrl_Enh)"
        }

    }

    class descriptor_1826448851() : HPCM_BCM_Service_eSrv(){
        private var _BCM_GraleLampDisp_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_GraleLampDisp_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_GraleLampDisp_Ctrl_Enh_In()

        init{
            _d = 1826448851
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_GraleLampDisp_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_GraleLampDisp_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_GraleLampDisp_Ctrl_Enh_In
            get() = _BCM_GraleLampDisp_Ctrl_Enh
            set(value) {
                _BCM_GraleLampDisp_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_GraleLampDisp_Ctrl_Enh=$BCM_GraleLampDisp_Ctrl_Enh)"
        }

    }

    class descriptor__986204298() : HPCM_BCM_Service_eSrv(){
        private var _BCM_ADASLampDisp_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_ADASLampDisp_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_ADASLampDisp_Ctrl_Enh_In()

        init{
            _d = -986204298
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_ADASLampDisp_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_ADASLampDisp_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_ADASLampDisp_Ctrl_Enh_In
            get() = _BCM_ADASLampDisp_Ctrl_Enh
            set(value) {
                _BCM_ADASLampDisp_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_ADASLampDisp_Ctrl_Enh=$BCM_ADASLampDisp_Ctrl_Enh)"
        }

    }

    class descriptor__898087379() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Window_ChildLock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Window_ChildLock_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Window_ChildLock_Ctrl_Enh_In()

        init{
            _d = -898087379
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Window_ChildLock_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Window_ChildLock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Window_ChildLock_Ctrl_Enh_In
            get() = _BCM_Window_ChildLock_Ctrl_Enh
            set(value) {
                _BCM_Window_ChildLock_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Window_ChildLock_Ctrl_Enh=$BCM_Window_ChildLock_Ctrl_Enh)"
        }

    }

    class descriptor_526666448() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Door_ChildLock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Door_ChildLock_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Door_ChildLock_Ctrl_Enh_In()

        init{
            _d = 526666448
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Door_ChildLock_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Door_ChildLock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Door_ChildLock_Ctrl_Enh_In
            get() = _BCM_Door_ChildLock_Ctrl_Enh
            set(value) {
                _BCM_Door_ChildLock_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Door_ChildLock_Ctrl_Enh=$BCM_Door_ChildLock_Ctrl_Enh)"
        }

    }

    class descriptor__1803624607() : HPCM_BCM_Service_eSrv(){
        private var _BCM_CentralLock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_CentralLock_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_CentralLock_Ctrl_Enh_In()

        init{
            _d = -1803624607
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_CentralLock_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_CentralLock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_CentralLock_Ctrl_Enh_In
            get() = _BCM_CentralLock_Ctrl_Enh
            set(value) {
                _BCM_CentralLock_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_CentralLock_Ctrl_Enh=$BCM_CentralLock_Ctrl_Enh)"
        }

    }

    class descriptor__834664438() : HPCM_BCM_Service_eSrv(){
        private var _BCM_DrvLock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_DrvLock_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_DrvLock_Ctrl_Enh_In()

        init{
            _d = -834664438
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_DrvLock_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_DrvLock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_DrvLock_Ctrl_Enh_In
            get() = _BCM_DrvLock_Ctrl_Enh
            set(value) {
                _BCM_DrvLock_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_DrvLock_Ctrl_Enh=$BCM_DrvLock_Ctrl_Enh)"
        }

    }

    class descriptor_1417955318() : HPCM_BCM_Service_eSrv(){
        private var _BCM_AwayLock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_AwayLock_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_AwayLock_Ctrl_Enh_In()

        init{
            _d = 1417955318
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_AwayLock_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_AwayLock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_AwayLock_Ctrl_Enh_In
            get() = _BCM_AwayLock_Ctrl_Enh
            set(value) {
                _BCM_AwayLock_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_AwayLock_Ctrl_Enh=$BCM_AwayLock_Ctrl_Enh)"
        }

    }

    class descriptor_978307593() : HPCM_BCM_Service_eSrv(){
        private var _BCM_approachunlock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_approachunlock_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_approachunlock_Ctrl_Enh_In()

        init{
            _d = 978307593
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_approachunlock_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_approachunlock_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_approachunlock_Ctrl_Enh_In
            get() = _BCM_approachunlock_Ctrl_Enh
            set(value) {
                _BCM_approachunlock_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_approachunlock_Ctrl_Enh=$BCM_approachunlock_Ctrl_Enh)"
        }

    }

    class descriptor__392039341() : HPCM_BCM_Service_eSrv(){
        private var _BCM_SeatCS_Enh: Seres.HPCM_BCM_eSrv.BCM_SeatCS_Enh_In = Seres.HPCM_BCM_eSrv.BCM_SeatCS_Enh_In()

        init{
            _d = -392039341
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_SeatCS_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_SeatCS_Enh: Seres.HPCM_BCM_eSrv.BCM_SeatCS_Enh_In
            get() = _BCM_SeatCS_Enh
            set(value) {
                _BCM_SeatCS_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_SeatCS_Enh=$BCM_SeatCS_Enh)"
        }

    }

    class descriptor_816261238() : HPCM_BCM_Service_eSrv(){
        private var _BCM_SideDoor_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_SideDoor_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_SideDoor_Ctrl_Enh_In()

        init{
            _d = 816261238
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_SideDoor_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_SideDoor_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_SideDoor_Ctrl_Enh_In
            get() = _BCM_SideDoor_Ctrl_Enh
            set(value) {
                _BCM_SideDoor_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_SideDoor_Ctrl_Enh=$BCM_SideDoor_Ctrl_Enh)"
        }

    }

    class descriptor_949607905() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Window_LockUp_Enh: Seres.HPCM_BCM_eSrv.BCM_Window_LockUp_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Window_LockUp_Enh_In()

        init{
            _d = 949607905
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Window_LockUp_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Window_LockUp_Enh: Seres.HPCM_BCM_eSrv.BCM_Window_LockUp_Enh_In
            get() = _BCM_Window_LockUp_Enh
            set(value) {
                _BCM_Window_LockUp_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Window_LockUp_Enh=$BCM_Window_LockUp_Enh)"
        }

    }

    class descriptor__1717582714() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Window_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Window_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Window_Ctrl_Enh_In()

        init{
            _d = -1717582714
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Window_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Window_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Window_Ctrl_Enh_In
            get() = _BCM_Window_Ctrl_Enh
            set(value) {
                _BCM_Window_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Window_Ctrl_Enh=$BCM_Window_Ctrl_Enh)"
        }

    }

    class descriptor__655106044() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Wiper_Sensitivity_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Wiper_Sensitivity_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Wiper_Sensitivity_Ctrl_Enh_In()

        init{
            _d = -655106044
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Wiper_Sensitivity_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Wiper_Sensitivity_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Wiper_Sensitivity_Ctrl_Enh_In
            get() = _BCM_Wiper_Sensitivity_Ctrl_Enh
            set(value) {
                _BCM_Wiper_Sensitivity_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Wiper_Sensitivity_Ctrl_Enh=$BCM_Wiper_Sensitivity_Ctrl_Enh)"
        }

    }

    class descriptor_92092396() : HPCM_BCM_Service_eSrv(){
        private var _BCM_Wiper_Repair_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Wiper_Repair_Ctrl_Enh_In = Seres.HPCM_BCM_eSrv.BCM_Wiper_Repair_Ctrl_Enh_In()

        init{
            _d = 92092396
            orderedMembers = arrayListOf(
                Member("__d", false),
                Member("_BCM_Wiper_Repair_Ctrl_Enh", false))

            __u = this

            initproperty()
        }

        var BCM_Wiper_Repair_Ctrl_Enh: Seres.HPCM_BCM_eSrv.BCM_Wiper_Repair_Ctrl_Enh_In
            get() = _BCM_Wiper_Repair_Ctrl_Enh
            set(value) {
                _BCM_Wiper_Repair_Ctrl_Enh = value
            }

        override fun toString(): String {
            return "$typename(_d = $_d, BCM_Wiper_Repair_Ctrl_Enh=$BCM_Wiper_Repair_Ctrl_Enh)"
        }

    }

    var _u_descriptor_1656882926: descriptor_1656882926
    get(){
        __u?.let{
            if ((__u as descriptor_1656882926)._d == 1656882926){
                return __u!! as descriptor_1656882926
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__920425368: descriptor__920425368
    get(){
        __u?.let{
            if ((__u as descriptor__920425368)._d == -920425368){
                return __u!! as descriptor__920425368
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_1822396041: descriptor_1822396041
    get(){
        __u?.let{
            if ((__u as descriptor_1822396041)._d == 1822396041){
                return __u!! as descriptor_1822396041
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_1856911191: descriptor_1856911191
    get(){
        __u?.let{
            if ((__u as descriptor_1856911191)._d == 1856911191){
                return __u!! as descriptor_1856911191
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_66238767: descriptor_66238767
    get(){
        __u?.let{
            if ((__u as descriptor_66238767)._d == 66238767){
                return __u!! as descriptor_66238767
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_655960075: descriptor_655960075
    get(){
        __u?.let{
            if ((__u as descriptor_655960075)._d == 655960075){
                return __u!! as descriptor_655960075
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_505398926: descriptor_505398926
    get(){
        __u?.let{
            if ((__u as descriptor_505398926)._d == 505398926){
                return __u!! as descriptor_505398926
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_207009957: descriptor_207009957
    get(){
        __u?.let{
            if ((__u as descriptor_207009957)._d == 207009957){
                return __u!! as descriptor_207009957
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__763709823: descriptor__763709823
    get(){
        __u?.let{
            if ((__u as descriptor__763709823)._d == -763709823){
                return __u!! as descriptor__763709823
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__1478517379: descriptor__1478517379
    get(){
        __u?.let{
            if ((__u as descriptor__1478517379)._d == -1478517379){
                return __u!! as descriptor__1478517379
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__950422755: descriptor__950422755
    get(){
        __u?.let{
            if ((__u as descriptor__950422755)._d == -950422755){
                return __u!! as descriptor__950422755
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__1403693521: descriptor__1403693521
    get(){
        __u?.let{
            if ((__u as descriptor__1403693521)._d == -1403693521){
                return __u!! as descriptor__1403693521
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_100069557: descriptor_100069557
    get(){
        __u?.let{
            if ((__u as descriptor_100069557)._d == 100069557){
                return __u!! as descriptor_100069557
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__1750758886: descriptor__1750758886
    get(){
        __u?.let{
            if ((__u as descriptor__1750758886)._d == -1750758886){
                return __u!! as descriptor__1750758886
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_1305593815: descriptor_1305593815
    get(){
        __u?.let{
            if ((__u as descriptor_1305593815)._d == 1305593815){
                return __u!! as descriptor_1305593815
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__1694013410: descriptor__1694013410
    get(){
        __u?.let{
            if ((__u as descriptor__1694013410)._d == -1694013410){
                return __u!! as descriptor__1694013410
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__1484169858: descriptor__1484169858
    get(){
        __u?.let{
            if ((__u as descriptor__1484169858)._d == -1484169858){
                return __u!! as descriptor__1484169858
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_1826448851: descriptor_1826448851
    get(){
        __u?.let{
            if ((__u as descriptor_1826448851)._d == 1826448851){
                return __u!! as descriptor_1826448851
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__986204298: descriptor__986204298
    get(){
        __u?.let{
            if ((__u as descriptor__986204298)._d == -986204298){
                return __u!! as descriptor__986204298
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__898087379: descriptor__898087379
    get(){
        __u?.let{
            if ((__u as descriptor__898087379)._d == -898087379){
                return __u!! as descriptor__898087379
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_526666448: descriptor_526666448
    get(){
        __u?.let{
            if ((__u as descriptor_526666448)._d == 526666448){
                return __u!! as descriptor_526666448
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__1803624607: descriptor__1803624607
    get(){
        __u?.let{
            if ((__u as descriptor__1803624607)._d == -1803624607){
                return __u!! as descriptor__1803624607
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__834664438: descriptor__834664438
    get(){
        __u?.let{
            if ((__u as descriptor__834664438)._d == -834664438){
                return __u!! as descriptor__834664438
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_1417955318: descriptor_1417955318
    get(){
        __u?.let{
            if ((__u as descriptor_1417955318)._d == 1417955318){
                return __u!! as descriptor_1417955318
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_978307593: descriptor_978307593
    get(){
        __u?.let{
            if ((__u as descriptor_978307593)._d == 978307593){
                return __u!! as descriptor_978307593
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__392039341: descriptor__392039341
    get(){
        __u?.let{
            if ((__u as descriptor__392039341)._d == -392039341){
                return __u!! as descriptor__392039341
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_816261238: descriptor_816261238
    get(){
        __u?.let{
            if ((__u as descriptor_816261238)._d == 816261238){
                return __u!! as descriptor_816261238
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_949607905: descriptor_949607905
    get(){
        __u?.let{
            if ((__u as descriptor_949607905)._d == 949607905){
                return __u!! as descriptor_949607905
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__1717582714: descriptor__1717582714
    get(){
        __u?.let{
            if ((__u as descriptor__1717582714)._d == -1717582714){
                return __u!! as descriptor__1717582714
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor__655106044: descriptor__655106044
    get(){
        __u?.let{
            if ((__u as descriptor__655106044)._d == -655106044){
                return __u!! as descriptor__655106044
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }

    var _u_descriptor_92092396: descriptor_92092396
    get(){
        __u?.let{
            if ((__u as descriptor_92092396)._d == 92092396){
                return __u!! as descriptor_92092396
            }else{
                throw IllegalArgumentException("Error: union _d not match")
            }
        }?: throw IllegalArgumentException("Error: _u is not set")
    }

    set(@Suppress("UNUSED_PARAMETER") value){
        throw IllegalArgumentException("Error: can not set value to _u")
    }


}

