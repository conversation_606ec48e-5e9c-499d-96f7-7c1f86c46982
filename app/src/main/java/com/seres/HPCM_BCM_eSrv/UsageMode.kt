package Seres.HPCM_BCM_eSrv

enum class UsageMode(var basevalue : Int){
    USAGEMODE_ABANDONED(0),
    USAGEMODE_INACTIVE(1),
    USAGEMODE_CONVENIENCE(2),
    USAGEMODE_DRIVING(3),
    USAGEMODE_OTAUPDATING(4),
    USAGEMODE_REMOTE(5),
    USAGEMODE_REMOTEDRIVING(6),
    USAGEMODE_RESERVED(7),
    USAGEMODE_INVALID(15);


    companion object {
        private val valueMap = UsageMode.entries.associateBy { it.basevalue }
        fun fromValue(basevalue: Int): UsageMode{
            return  valueMap[basevalue]?:USAGEMODE_ABANDONED
        }    
    }
}
