package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class HPCC_SeatPosition : TypeStruct() {
    private var _seat_fl : Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition()
    private var _seat_fr : Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition()
    private var _seat_rl : Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition()
    private var _seat_rm : Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition()
    private var _seat_rr : Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition()
    private var _seat_thirdl : Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition()
    private var _seat_thirdm : Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition()
    private var _seat_thirdr : Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition = Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition()

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::HPCC_SeatPosition"
        orderedMembers = arrayListOf(
            Member("_seat_fl", false),
            Member("_seat_fr", false),
            Member("_seat_rl", false),
            Member("_seat_rm", false),
            Member("_seat_rr", false),
            Member("_seat_thirdl", false),
            Member("_seat_thirdm", false),
            Member("_seat_thirdr", false),
        )

        initproperty()
    }

    var seat_fl: Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition
        get() = _seat_fl
        set(value){
            _seat_fl = value
        }

    var seat_fr: Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition
        get() = _seat_fr
        set(value){
            _seat_fr = value
        }

    var seat_rl: Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition
        get() = _seat_rl
        set(value){
            _seat_rl = value
        }

    var seat_rm: Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition
        get() = _seat_rm
        set(value){
            _seat_rm = value
        }

    var seat_rr: Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition
        get() = _seat_rr
        set(value){
            _seat_rr = value
        }

    var seat_thirdl: Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition
        get() = _seat_thirdl
        set(value){
            _seat_thirdl = value
        }

    var seat_thirdm: Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition
        get() = _seat_thirdm
        set(value){
            _seat_thirdm = value
        }

    var seat_thirdr: Seres.HPCM_BCM_eSrv.HPCC_SeatInstancePosition
        get() = _seat_thirdr
        set(value){
            _seat_thirdr = value
        }

    fun copy(value: HPCC_SeatPosition = this): HPCC_SeatPosition{
            this._seat_fl =  value._seat_fl
            this._seat_fr =  value._seat_fr
            this._seat_rl =  value._seat_rl
            this._seat_rm =  value._seat_rm
            this._seat_rr =  value._seat_rr
            this._seat_thirdl =  value._seat_thirdl
            this._seat_thirdm =  value._seat_thirdm
            this._seat_thirdr =  value._seat_thirdr
            return this
        }

    override fun toString(): String{
        return "$typename(seat_fl=$seat_fl, seat_fr=$seat_fr, seat_rl=$seat_rl, seat_rm=$seat_rm, seat_rr=$seat_rr, seat_thirdl=$seat_thirdl, seat_thirdm=$seat_thirdm, seat_thirdr=$seat_thirdr)"
    }
}

