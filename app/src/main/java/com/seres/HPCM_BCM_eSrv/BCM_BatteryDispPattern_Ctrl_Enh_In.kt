package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_BatteryDispPattern_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.HPCM_BCM_eSrv.BCM_CallerID = 0u
    private var _intelsigdisp_op : Seres.HPCM_BCM_eSrv.HPCC_IntelSigDisp_Op = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::BCM_BatteryDispPattern_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_intelsigdisp_op", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.HPCM_BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var intelsigdisp_op: Seres.HPCM_BCM_eSrv.HPCC_IntelSigDisp_Op
        get() = _intelsigdisp_op
        set(value){
            _intelsigdisp_op = value
        }

    fun copy(value: BCM_BatteryDispPattern_Ctrl_Enh_In = this): BCM_BatteryDispPattern_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._intelsigdisp_op =  value._intelsigdisp_op
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, intelsigdisp_op=$intelsigdisp_op)"
    }
}

