package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Light_BeamPosition_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.HPCM_BCM_eSrv.BCM_CallerID = 0u
    private var _zangle : Seres.HPCM_BCM_eSrv.HPCC_Zangle = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::BCM_Light_BeamPosition_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_zangle", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.HPCM_BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var zangle: Seres.HPCM_BCM_eSrv.HPCC_Zangle
        get() = _zangle
        set(value){
            _zangle = value
        }

    fun copy(value: BCM_Light_BeamPosition_Ctrl_Enh_In = this): BCM_Light_BeamPosition_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._zangle =  value._zangle
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, zangle=$zangle)"
    }
}

