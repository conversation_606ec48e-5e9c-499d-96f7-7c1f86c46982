package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Extrlight_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.HPCM_BCM_eSrv.BCM_CallerID = 0u
    private var _cfgpara : Seres.HPCM_BCM_eSrv.HPCC_CfgPara = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::BCM_Extrlight_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_cfgpara", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.HPCM_BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var cfgpara: Seres.HPCM_BCM_eSrv.HPCC_CfgPara
        get() = _cfgpara
        set(value){
            _cfgpara = value
        }

    fun copy(value: BCM_Extrlight_Ctrl_Enh_In = this): BCM_Extrlight_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._cfgpara =  value._cfgpara
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, cfgpara=$cfgpara)"
    }
}

