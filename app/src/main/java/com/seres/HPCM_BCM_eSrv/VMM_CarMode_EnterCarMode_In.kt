package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class VMM_CarMode_EnterCarMode_In : TypeStruct() {
    private var _appsrv_callerid : Seres.HPCM_BCM_eSrv.AppSrv_CallerId = 0u
    private var _carmode : Seres.HPCM_BCM_eSrv.CarMode = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::VMM_CarMode_EnterCarMode_In"
        orderedMembers = arrayListOf(
            Member("_appsrv_callerid", false),
            Member("_carmode", false),
        )

        initproperty()
    }

    var appsrv_callerid: Seres.HPCM_BCM_eSrv.AppSrv_CallerId
        get() = _appsrv_callerid
        set(value){
            _appsrv_callerid = value
        }

    var carmode: Seres.HPCM_BCM_eSrv.CarMode
        get() = _carmode
        set(value){
            _carmode = value
        }

    fun copy(value: VMM_CarMode_EnterCarMode_In = this): VMM_CarMode_EnterCarMode_In{
            this._appsrv_callerid =  value._appsrv_callerid
            this._carmode =  value._carmode
            return this
        }

    override fun toString(): String{
        return "$typename(appsrv_callerid=$appsrv_callerid, carmode=$carmode)"
    }
}

