package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_Window_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.HPCM_BCM_eSrv.BCM_CallerID = 0u
    private var _windowid : Seres.HPCM_BCM_eSrv.HPCC_WindowID = 0u
    private var _windowpos : Seres.HPCM_BCM_eSrv.HPCC_WindowPosition = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::BCM_Window_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_windowid", false),
            Member("_windowpos", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.HPCM_BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var windowid: Seres.HPCM_BCM_eSrv.HPCC_WindowID
        get() = _windowid
        set(value){
            _windowid = value
        }

    var windowpos: Seres.HPCM_BCM_eSrv.HPCC_WindowPosition
        get() = _windowpos
        set(value){
            _windowpos = value
        }

    fun copy(value: BCM_Window_Ctrl_Enh_In = this): BCM_Window_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._windowid =  value._windowid
            this._windowpos =  value._windowpos
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, windowid=$windowid, windowpos=$windowpos)"
    }
}

