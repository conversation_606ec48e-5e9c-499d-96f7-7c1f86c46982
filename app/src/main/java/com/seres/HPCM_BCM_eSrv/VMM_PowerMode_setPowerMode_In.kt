package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class VMM_PowerMode_setPowerMode_In : TypeStruct() {
    private var _appsrv_calleridarg : Seres.HPCM_BCM_eSrv.AppSrv_CallerId = 0u
    private var _powermodearg : Int = Seres.HPCM_BCM_eSrv.PowerMode.POWERMODE_OFF.basevalue

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::VMM_PowerMode_setPowerMode_In"
        orderedMembers = arrayListOf(
            Member("_appsrv_calleridarg", false),
            Member("_powermodearg", false),
        )

        initproperty()
    }

    var appsrv_calleridarg: Seres.HPCM_BCM_eSrv.AppSrv_CallerId
        get() = _appsrv_calleridarg
        set(value){
            _appsrv_calleridarg = value
        }

    var powermodearg: Seres.HPCM_BCM_eSrv.PowerMode
        get() = Seres.HPCM_BCM_eSrv.PowerMode.values().first { it.basevalue == _powermodearg }
        set(value){
            _powermodearg = value.basevalue
        }

    fun copy(value: VMM_PowerMode_setPowerMode_In = this): VMM_PowerMode_setPowerMode_In{
            this._appsrv_calleridarg =  value._appsrv_calleridarg
            this._powermodearg =  value._powermodearg
            return this
        }

    override fun toString(): String{
        return "$typename(appsrv_calleridarg=$appsrv_calleridarg, powermodearg=$powermodearg)"
    }
}

