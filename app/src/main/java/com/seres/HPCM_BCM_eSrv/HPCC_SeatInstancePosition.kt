package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class HPCC_SeatInstancePosition : TypeStruct() {
    private var _mainxdir : Seres.HPCM_BCM_eSrv.Percent = 0u
    private var _mainzdir : Seres.HPCM_BCM_eSrv.Percent = 0u
    private var _frontzdir : Seres.HPCM_BCM_eSrv.Percent = 0u
    private var _backrestangle : Seres.HPCM_BCM_eSrv.Percent = 0u
    private var _legrestangle : Seres.HPCM_BCM_eSrv.Percent = 0u
    private var _legrestxdir : Seres.HPCM_BCM_eSrv.Percent = 0u
    private var _footrestangle : Seres.HPCM_BCM_eSrv.Percent = 0u
    private var _frontxdir : Seres.HPCM_BCM_eSrv.Percent = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::HPCC_SeatInstancePosition"
        orderedMembers = arrayListOf(
            Member("_mainxdir", false),
            Member("_mainzdir", false),
            Member("_frontzdir", false),
            Member("_backrestangle", false),
            Member("_legrestangle", false),
            Member("_legrestxdir", false),
            Member("_footrestangle", false),
            Member("_frontxdir", false),
        )

        initproperty()
    }

    var mainxdir: Seres.HPCM_BCM_eSrv.Percent
        get() = _mainxdir
        set(value){
            _mainxdir = value
        }

    var mainzdir: Seres.HPCM_BCM_eSrv.Percent
        get() = _mainzdir
        set(value){
            _mainzdir = value
        }

    var frontzdir: Seres.HPCM_BCM_eSrv.Percent
        get() = _frontzdir
        set(value){
            _frontzdir = value
        }

    var backrestangle: Seres.HPCM_BCM_eSrv.Percent
        get() = _backrestangle
        set(value){
            _backrestangle = value
        }

    var legrestangle: Seres.HPCM_BCM_eSrv.Percent
        get() = _legrestangle
        set(value){
            _legrestangle = value
        }

    var legrestxdir: Seres.HPCM_BCM_eSrv.Percent
        get() = _legrestxdir
        set(value){
            _legrestxdir = value
        }

    var footrestangle: Seres.HPCM_BCM_eSrv.Percent
        get() = _footrestangle
        set(value){
            _footrestangle = value
        }

    var frontxdir: Seres.HPCM_BCM_eSrv.Percent
        get() = _frontxdir
        set(value){
            _frontxdir = value
        }

    fun copy(value: HPCC_SeatInstancePosition = this): HPCC_SeatInstancePosition{
            this._mainxdir =  value._mainxdir
            this._mainzdir =  value._mainzdir
            this._frontzdir =  value._frontzdir
            this._backrestangle =  value._backrestangle
            this._legrestangle =  value._legrestangle
            this._legrestxdir =  value._legrestxdir
            this._footrestangle =  value._footrestangle
            this._frontxdir =  value._frontxdir
            return this
        }

    override fun toString(): String{
        return "$typename(mainxdir=$mainxdir, mainzdir=$mainzdir, frontzdir=$frontzdir, backrestangle=$backrestangle, legrestangle=$legrestangle, legrestxdir=$legrestxdir, footrestangle=$footrestangle, frontxdir=$frontxdir)"
    }
}

