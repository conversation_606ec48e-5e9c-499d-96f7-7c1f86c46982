package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class VMM_UsageMode_EnterUsageMode_In : TypeStruct() {
    private var _appsrv_calleridarg : Seres.HPCM_BCM_eSrv.AppSrv_CallerId = 0u
    private var _usagemodearg : Int = Seres.HPCM_BCM_eSrv.UsageMode.USAGEMODE_ABANDONED.basevalue

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::VMM_UsageMode_EnterUsageMode_In"
        orderedMembers = arrayListOf(
            Member("_appsrv_calleridarg", false),
            Member("_usagemodearg", false),
        )

        initproperty()
    }

    var appsrv_calleridarg: Seres.HPCM_BCM_eSrv.AppSrv_CallerId
        get() = _appsrv_calleridarg
        set(value){
            _appsrv_calleridarg = value
        }

    var usagemodearg: Seres.HPCM_BCM_eSrv.UsageMode
        get() = Seres.HPCM_BCM_eSrv.UsageMode.values().first { it.basevalue == _usagemodearg }
        set(value){
            _usagemodearg = value.basevalue
        }

    fun copy(value: VMM_UsageMode_EnterUsageMode_In = this): VMM_UsageMode_EnterUsageMode_In{
            this._appsrv_calleridarg =  value._appsrv_calleridarg
            this._usagemodearg =  value._usagemodearg
            return this
        }

    override fun toString(): String{
        return "$typename(appsrv_calleridarg=$appsrv_calleridarg, usagemodearg=$usagemodearg)"
    }
}

