package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class VMM_UsageMode_SetExitDelayTi_In : TypeStruct() {
    private var _appsrv_calleridarg : Seres.HPCM_BCM_eSrv.AppSrv_CallerId = 0u
    private var _exitdelaytiarg : Seres.HPCM_BCM_eSrv.ExitDelayTi = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::VMM_UsageMode_SetExitDelayTi_In"
        orderedMembers = arrayListOf(
            Member("_appsrv_calleridarg", false),
            Member("_exitdelaytiarg", false),
        )

        initproperty()
    }

    var appsrv_calleridarg: Seres.HPCM_BCM_eSrv.AppSrv_CallerId
        get() = _appsrv_calleridarg
        set(value){
            _appsrv_calleridarg = value
        }

    var exitdelaytiarg: Seres.HPCM_BCM_eSrv.ExitDelayTi
        get() = _exitdelaytiarg
        set(value){
            _exitdelaytiarg = value
        }

    fun copy(value: VMM_UsageMode_SetExitDelayTi_In = this): VMM_UsageMode_SetExitDelayTi_In{
            this._appsrv_calleridarg =  value._appsrv_calleridarg
            this._exitdelaytiarg =  value._exitdelaytiarg
            return this
        }

    override fun toString(): String{
        return "$typename(appsrv_calleridarg=$appsrv_calleridarg, exitdelaytiarg=$exitdelaytiarg)"
    }
}

