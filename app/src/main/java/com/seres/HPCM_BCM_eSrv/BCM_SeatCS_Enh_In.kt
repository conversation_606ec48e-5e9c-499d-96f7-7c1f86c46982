package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_SeatCS_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.HPCM_BCM_eSrv.BCM_CallerID = 0u
    private var _seatposition : Seres.HPCM_BCM_eSrv.HPCC_SeatPosition = Seres.HPCM_BCM_eSrv.HPCC_SeatPosition()

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::BCM_SeatCS_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_seatposition", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.HPCM_BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var seatposition: Seres.HPCM_BCM_eSrv.HPCC_SeatPosition
        get() = _seatposition
        set(value){
            _seatposition = value
        }

    fun copy(value: BCM_SeatCS_Enh_In = this): BCM_SeatCS_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._seatposition =  value._seatposition
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, seatposition=$seatposition)"
    }
}

