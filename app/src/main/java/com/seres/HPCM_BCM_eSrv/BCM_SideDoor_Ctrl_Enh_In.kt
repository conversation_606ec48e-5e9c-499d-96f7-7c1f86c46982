package Seres.HPCM_BCM_eSrv


import com.seres.dds.sdk.*
import com.seres.dds.sdk.idl.*


class BCM_SideDoor_Ctrl_Enh_In : TypeStruct() {
    private var _bcm_callerid : Seres.HPCM_BCM_eSrv.BCM_CallerID = 0u
    private var _doorid : Seres.HPCM_BCM_eSrv.HPCC_DoorID = 0u
    private var _sidedoor_op : Seres.HPCM_BCM_eSrv.HPCC_SideDoor_Op = 0u

    init{
        keyless = true
        version_support = 1
        typename = "Seres::HPCM_BCM_eSrv::BCM_SideDoor_Ctrl_Enh_In"
        orderedMembers = arrayListOf(
            Member("_bcm_callerid", false),
            Member("_doorid", false),
            Member("_sidedoor_op", false),
        )

        initproperty()
    }

    var bcm_callerid: Seres.HPCM_BCM_eSrv.BCM_CallerID
        get() = _bcm_callerid
        set(value){
            _bcm_callerid = value
        }

    var doorid: Seres.HPCM_BCM_eSrv.HPCC_DoorID
        get() = _doorid
        set(value){
            _doorid = value
        }

    var sidedoor_op: Seres.HPCM_BCM_eSrv.HPCC_SideDoor_Op
        get() = _sidedoor_op
        set(value){
            _sidedoor_op = value
        }

    fun copy(value: BCM_SideDoor_Ctrl_Enh_In = this): BCM_SideDoor_Ctrl_Enh_In{
            this._bcm_callerid =  value._bcm_callerid
            this._doorid =  value._doorid
            this._sidedoor_op =  value._sidedoor_op
            return this
        }

    override fun toString(): String{
        return "$typename(bcm_callerid=$bcm_callerid, doorid=$doorid, sidedoor_op=$sidedoor_op)"
    }
}

