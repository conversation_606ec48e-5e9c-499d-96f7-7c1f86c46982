package Seres.HPCM_BCM_eSrv

enum class CS_WiperSensitivityLevel(var basevalue : Int){
    CS_WiperSensitivityLevel_NO_REQUEST(0),
    CS_WiperSensitivityLevel_LOW(1),
    CS_WiperSensitivityLevel_MEDIUM(2),
    CS_WiperSensitivityLevel_HIGH(3);


    companion object {
        private val valueMap = CS_WiperSensitivityLevel.entries.associateBy { it.basevalue }
        fun fromValue(basevalue: Int): CS_WiperSensitivityLevel{
            return  valueMap[basevalue]?:CS_WiperSensitivityLevel_NO_REQUEST
        }    
    }
}
